<?php if (!defined("BASEPATH")) exit("No direct script access allowed");

/* vim: set expandtab tabstop=4 shiftwidth=4 softtabstop=4: */

/** 
 *    ___  _____________  __  ________  __
 *   / _ )/ __/ ___/ __ \/  |/  / __/ |/_/
 *  / _  / _// /__/ /_/ / /|_/ / _/_>  <  
 * /____/___/\___/\____/_/  /_/___/_/|_|
 * 
 * Model para os atributos do cadastro de itens.
 * 
 * Model responsavel pelas requisições ao banco de dados relacionadas ao
 * atributo do cadastro de item.
 *  
 * PHP version 5
 *
 * LICENSE: This source file is subject to version 3.01 of the PHP license
 * that is available through the world-wide-web at the following URI:
 * http://www.php.net/license/3_01.txt.  If you did not receive a copy of
 * the PHP License and are unable to obtain it through the web, please
 * send a <NAME_EMAIL> so we can mail you a copy immediately.
 * 
 * @category  Gestão Tarifaria
 * @package   CodeIgniter
 * 
 * <AUTHOR>    <<EMAIL>>
 * <AUTHOR>            <<EMAIL>>
 * 
 * @copyright 1997-2005 The PHP Group
 * @license   http://www.php.net/license/3_01.txt  PHP License 3.01
 * @link      http://pear.php.net/package/PackageName
 * @see       CadItemAttrModel, Cad_Item_Attr_Model::Cad_Item_Attr_Model()
 */
class Cad_Item_Attr_Model extends MY_Model
{
    // {{{ Propriedades

    /**
     * A tabela em questão.
     * 
     * <AUTHOR> Santana <<EMAIL>>
     * 
     * @property string        $_table
     */
    private $_table = "cad_item_attr";

    /**
     * O ID do usuario atual.
     * 
     * <AUTHOR> Santana <<EMAIL>>
     * 
     * @property null|int      $_user_id
     */
    private $_user_id = NULL;

    private $attrs = [];
    private $alteracoes = [];
    // }}
    // {{ __construct()

    /**
     * Metodo construtor.
     * 
     * <AUTHOR> Santana <<EMAIL>>
     * 
     * @return void          Sem retorno.
     */
    public function __construct()
    {
        parent::__construct();

        if (!is_logged()) {
            redirect("/login");
        }

        $this->_user_id = sess_user_id();
    }

    public function remove_duplicate_records ($id_item_registro)
    {
        $this->db->query("
        DELETE FROM cad_item_attr
        WHERE (atributo, atualizado_em) NOT IN (
          SELECT t.atributo, t.max_atualizado_em
          FROM (
            SELECT atributo, MAX(atualizado_em) AS max_atualizado_em
            FROM cad_item_attr
            WHERE id_item = '{$id_item_registro}'
            GROUP BY atributo 
          ) t
        ) and id_item = '{$id_item_registro}';");
    }

    // }}
    // {{ save_attrs()

    /**
     * Salva uma lista de atributos na tabela.
     * 
     * <AUTHOR> Santana           <<EMAIL>>
     * 
     * @param  array         $arr_attr Os atributos base a ser utilizado, aceito tipo string para a captura
     *                                 feita via JQuery.
     * @param  array         $item     O item relacionado ao atributo.
     * 
     * @throws \Exception              Se ocorrer algum erro durante a execução.
     * @return void                    Sem retorno.
     */
    public function save_attrs($arr_attr, $item = null)
    {
        if (empty($arr_attr)) {
            throw new \Exception("Parametros indefinidos ou vazios.");
        }

        foreach($arr_attr as $attr) {
            if (\is_string($attr)) {
                $attr = \json_decode($attr, TRUE); // Força a conversão de string para o tipo array.
            }
        
            $dados_item = [];
            $dados_item['ncm'] = null;
            $dados_item['id_item'] = null;
            
            if (!empty($item))
            {
                if (is_array($item))
                {
                    $dados_item['ncm'] = reset($item)->ncm_proposto;
                    $dados_item['id_item'] = reset($item)->id_item;
                }
            }
    
            $this->save_attr($attr, $item, [], TRUE, $dados_item);
        }
 
        $this->load->model('cad_item_wf_atributo_model');
        $data = $this->cad_item_wf_atributo_model->organizeDataByItem($this->alteracoes);
        $data_result = $this->cad_item_wf_atributo_model->formatChangeLog($data);
        $this->cad_item_wf_atributo_model->log_alteracao_attr($data_result);
    }

    // }}
    // {{ save_attr()

    /**
     * Salva um atributo na tabela.
     * 
     * <AUTHOR> Santana              <<EMAIL>>
     * 
     * @param  array         $item        O item relacionado ao atributo.
     * @param  array         $attr        O atributo base a ser utilizado.
     * @param  array         $parent_attr O atributo pai na instancia, `empty` na primeira execução.
     * 
     * @throws \Exception                 Se ocorrer algum erro durante a execução.
     * @return void                       Sem retorno.
     */
    public function save_attr(&$attr, $itens, $parent_attr = array(), $check_update = false, $post = null)
    {
        $this->load->model('cad_item_model');

        $new_id = $updated_id = null;

        if (empty($attr) || empty($itens)) {
            throw new \Exception("Os parametros informados sao incorretos ou insuficientes.");
        }

        if (!is_array($attr) || !is_array($itens)) {
            throw new \Exception("Os parametros informados nao atendem aos requisitos necessarios");
        }
 
        if (empty($attr["dbdata"])) {
            throw new \Exception("Os valores para registro estao indefinidos.");
        }
        $itens = (!array_key_exists(0, $itens)) ? [$itens] : $itens;
        $codigo = $attr['dbdata']['codigo'];

        if (\is_array($attr['dbdata']['codigo'])) {
            $codigo = implode(",", $attr['dbdata']['codigo']);
        }
        
        $descricao = "";
        if (isset($attr["dominio"])) {
            foreach ($attr["dominio"] as $dominio) {
                if ($dominio["codigo"] == $codigo) {
                    $descricao = $dominio["descricao"];
                    break;
                }
            }
        }
        $ncm = '';
        if (!empty($post) && !empty($post['ncm']))
        {
            $ncm = $post['ncm'];
        }
 
        foreach ($itens as $item)
        {
            $item = !is_array($item) ? (array) $item : $item;
            $data = [
                "codigo"             => $codigo,
                "apresentacao"       => !empty($attr['dbdata']['apresentacao']) ? $attr['dbdata']['apresentacao'] : null,
                "descricao"          => $descricao,
                "atributo"           => $attr["codigo"],
                "id_grupo_tarifario" => $item["id_grupo_tarifario"],
                "id_item"            => $item["id_item"],
                "atualizado_em"      => date("Y-m-d H:i:s"),
                "obrigatorio"        => $attr["obrigatorio"]? 1 : 0,
                "id_usuario"         => $this->_user_id,
            ];
            $id_item = $item["id_item"];
            if ($attr['formaPreenchimento'] == 'COMPOSTO')
            {
                $data['codigo'] = 1;
            }
          
            $data["atributo_pai"] = null;
  
            if (!empty($parent_attr["codigo"])) {
                $data["atributo_pai"] = $parent_attr["codigo"];
            }

            if (!empty($parent_attr['atributo']['codigo'])) {
                $data["atributo_pai"] = $parent_attr['atributo']["codigo"];
            }

            $do_not_update = false;
            $value_not_empty = false;
            $jump_attr = false;
            if ($this->get_state('filter.do_not_update')) {
                $do_not_update = true;
            }
 
            $chave_attr = $item["id_item"].$attr["codigo"];
            if (!in_array($chave_attr, $this->attrs))
            {
                $this->attrs[] = $item["id_item"].$attr["codigo"];

                if ($this->get_state('filter.vinculacao')) {
                    $check_update = true;
                    $value_not_empty = true;
                }
                if ($value_not_empty && empty($data["codigo"]))
                {
                   $jump_attr = true;
                }

                if (!$jump_attr)
                {
                    if (!$check_update)
                    {
                        if ($do_not_update)
                        {
                            $attr_exists = $this->attr_already_exists($data);
                            if (empty($attr_exists))
                            {
                                $data["criado_em"] = \date("Y-m-d H:i:s");
                                $new_id = $this->insert_attr($data);
                            } 
                        } else {
                            $data["criado_em"] = \date("Y-m-d H:i:s");
                            $new_id = $this->insert_attr($data);
                        }
                    } else {
                        $attr_exists = $this->attr_already_exists($data);

                        if (empty($attr_exists))
                        {
                            $data["criado_em"] = \date("Y-m-d H:i:s");
                            $new_id = $this->insert_attr($data);
                            $attr_exists = $this->attr_already_exists($data);

                            $this->alteracoes[] = [
                                'attr_exists' => $attr_exists,
                                'data' => $data,
                                'dominio' => $attr['dominio'],
                                'formaPreenchimento' => $attr['formaPreenchimento'],
                                'new_id' => $new_id
                            ];

                        } else {
                            $data['id'] = $attr_exists->id;
                            $updated_id = $this->update_attr($data);

                            $this->alteracoes[] = [
                                'attr_exists' => $attr_exists,
                                'data' => $data,
                                'dominio' => $attr['dominio'],
                                'formaPreenchimento' => $attr['formaPreenchimento'],
                                'updated_id' => $updated_id
                            ];

                        }
                    }
                
                }
            }
 
                $data["id"]     = $new_id ?: $updated_id; // Captura o ID alvo e atribui aos dados enviados.

                $attr["dbdata"] = $data;
    
                if ($attr["atributoCondicionante"] == TRUE && !empty($attr['condicionados']))
                {
                    foreach ($attr["condicionados"] as &$cond) {
                        if (!empty($cond["atributo"]["condicionados"]))
                        {
                            foreach ($cond["atributo"]["condicionados"] as &$cond_attr) {

                                $this->save_attr($cond_attr["atributo"], $item, $cond, TRUE);
                            }
                        }
                        if ($this->has_attr_cond($attr, $cond))
                        {
                            $this->save_attr($cond["atributo"], $item, $attr, TRUE);
                        } else
                        {
                            if ($check_update && !empty($cond["atributo"]["dbdata"]["codigo"]))
                            {
                                $this->save_attr($cond["atributo"], $item, $attr, TRUE);
                            } elseif ($check_update && empty($cond["atributo"]["dbdata"]["codigo"]))
                            {
                                // Verificar se o atributo existe. Se sim, atualizar ou deletar ele pois o usuário pode estar simplesmente removendo o valor.
                                $dataAttr = [
                                    "codigo"             => "",
                                    "apresentacao"       => "",
                                    "descricao"          => "",
                                    "atributo"           => $cond["atributo"]["codigo"],
                                    "id_grupo_tarifario" => $item["id_grupo_tarifario"],
                                    "id_item"            => $item["id_item"],
                                    "atualizado_em"      => date("Y-m-d H:i:s"),
                                    "obrigatorio"        => $cond["atributo"]["obrigatorio"]? 1 : 0,
                                    "id_usuario"         => $this->_user_id,
                                ];
                                $attr_exists = $this->attr_already_exists($dataAttr);
                                
                                if (!empty($attr_exists))
                                {
                                    $dataAttr["id"] = $attr_exists->id;
                                    $this->update_attr($dataAttr);
                                }
                            }
                            $cond["atributo"]["dbdata"] = [ "codigo" => "" ];
                        }
                    }
                }

                if (\strtoupper($attr["formaPreenchimento"]) == "COMPOSTO") {
                    foreach ($attr["listaSubatributos"] as &$sub_attr) {
                        $this->save_attr($sub_attr, $item, $attr, TRUE);
                    }
                }

                if (!empty($id_item) && !empty($ncm))
                {
                    $this->cad_item_model->save_status_attr($id_item, $ncm, true);
                }
        }

        if(isset($post['is_ficha']) && $post['is_ficha']) {
            $this->load->model('cad_item_wf_atributo_model');
        
            $data = $this->cad_item_wf_atributo_model->organizeDataByItem($this->alteracoes);
            $data_result = $this->cad_item_wf_atributo_model->formatChangeLog($data);
            $this->cad_item_wf_atributo_model->log_alteracao_attr($data_result);
        }
        
    }

    // }}
    // {{ has_attr_cond()

    /**
     * Valida se um atributo satisfaz a sua condição para ser lido.
     * 
     * <AUTHOR> Santana       <<EMAIL>>
     * 
     * @param  array         $attr O atributo alvo.
     * @param  array         $cond A condição para ser analizada.
     * 
     * @return bool                Se satisfaz a condição.
     */
    private function has_attr_cond($attr, $cond)
    {
        $cond_op  = $cond["condicao"]["operador"]; // O operador para comparação.
        $cond_val = $cond["condicao"]["valor"];    // O valor para ser comparado.
        $cond_res = FALSE;                         // O resultado final da comparação.

        if (!empty($attr['multivalorado'])) {
            $attr_vals = $attr["dbdata"]["codigo"]; // Multivalorado.

            if (!\is_array($attr_vals)) {
                $attr_vals = explode(",", $attr_vals);
            }

            foreach($attr_vals as $attr_val) {

                $cond_res = eval("return \"$attr_val\" $cond_op \"$cond_val\";");

                if ($cond_res) {
                    break;
                }
            }

        } else {
            $attr_val = $attr["dbdata"]["codigo"]; // Valor simples.

            if (strtoupper($attr["formaPreenchimento"]) == 'BOOLEANO' && $attr_val > 0) {
                $attr_val = "true";
            }

            $cond_res = eval("return \"$attr_val\" $cond_op \"$cond_val\";");
        }

        return $cond_res;
    }

    public function update_cad_item_attr_ncm($part_number, $id_empresa, $estabelecimento, $grupo, $id_grupo_tarifario)
    {
        $cad_item_entry_n = $this->cad_item_model->get_entry_by_pn($part_number, $id_empresa, $estabelecimento);

        $attrs_item = (array) $this->get_attr($cad_item_entry_n->id_item);
        if (empty($attrs_item)) {
            $this->db->where('ncm', $grupo->ncm_recomendada);
            $query = $this->db->get('ncm_atributo');
            $attrs_pai =  $query->result();

            if (!empty($attrs_pai))
            {
                foreach ($attrs_pai as  $atributo )
                {
                    $atributo = (array) $atributo;
 
                    $descricao = "";
                    if (!empty($atributo["dominio"])) {
           
                        $dominioArray = is_array($atributo["dominio"]) 
                            ? $atributo["dominio"] 
                            : json_decode($atributo["dominio"], true);
                    
                        if (is_array($dominioArray)) {
                            foreach ($dominioArray as $dominio) {
                                if ($dominio["codigo"] == $atributo['codigo']) {
                                    $descricao = $dominio["descricao"];
                                    break;
                                }
                            }
                        }
                    }
                    
                    $dbdata = array(
                        'codigo' => '',
                        'apresentacao' => $atributo['nome_apresentacao'],
                        'descricao' => $descricao,
                        'atributo_pai' => $atributo['codigo_pai'],
                        'atributo' => $atributo['codigo'],
                        'obrigatorio' => $atributo['obrigatorio'],
                        'id_item' => $cad_item_entry_n->id_item,
                        'id_grupo_tarifario' => $id_grupo_tarifario,
                        'id_usuario' => 1,
                        'modalidade' => $atributo['modalidade'],
                        'criado_em' =>  \date("Y-m-d H:i:s"),
                        'atualizado_em' => \date("Y-m-d H:i:s"),
                        'ativo' => 1
                    );

                    $this->db->insert('cad_item_attr', $dbdata);

                }
            }

        } else {

            $this->db->select('codigo');
            $this->db->where('ncm', $grupo->ncm_recomendada);
            $query = $this->db->get('ncm_atributo');
            $attrs_pai =  $query->result();

            $atributos = array_map(function($item) {
                return $item->atributo;
            }, $attrs_item);

            foreach ($attrs_pai as $item) {
                
                if (!in_array($item->codigo, $atributos)) {

                    $this->db->where('ncm', $grupo->ncm_recomendada);
                    $this->db->where('codigo', $item->codigo);
                    $query = $this->db->get('ncm_atributo');
                    $atributo =  $query->row();
       
                    $atributo = (array) $atributo;

                    if (!empty($atributo))
                    {
                        $descricao = "";
                        if (!empty($atributo["dominio"])) {
           
                            $dominioArray = is_array($atributo["dominio"]) 
                                ? $atributo["dominio"] 
                                : json_decode($atributo["dominio"], true);
                        
                            if (is_array($dominioArray)) {
                                foreach ($dominioArray as $dominio) {
                                    if ($dominio["codigo"] == $atributo['codigo']) {
                                        $descricao = $dominio["descricao"];
                                        break;
                                    }
                                }
                            }
                        }

                        $dbdata = array(
                            'codigo' => '',
                            'apresentacao' => $atributo['nome_apresentacao'],
                            'descricao' => $descricao,
                            'atributo_pai' => $atributo['codigo_pai'],
                            'atributo' => $atributo['codigo'],
                            'obrigatorio' => $atributo['obrigatorio'],
                            'id_item' => $cad_item_entry_n->id_item,
                            'id_grupo_tarifario' => $id_grupo_tarifario,
                            'id_usuario' => 1,
                            'modalidade' => $atributo['modalidade'],
                            'criado_em' =>  \date("Y-m-d H:i:s"),
                            'atualizado_em' => \date("Y-m-d H:i:s"),
                            'ativo' => 1
                        );
            
                        $this->db->insert('cad_item_attr', $dbdata);
                    }

                }  
            }
        }

        return true;

    }

    // }}
    // {{ attr_already_exists()

    /**
     * Valida se um atributo ja existe.
     * 
     * <AUTHOR> Santana       <<EMAIL>>
     * 
     * @param  array         $data Os valores a serem atualizados/inseridos. Deve seguir a mesma nomenclatura
     *                             de colunas e estrutura da tabela.
     * 
     * @return object|null            O ID encontrado.
     */
    public function attr_already_exists($data)
    {
        $apresentacao = htmlspecialchars($data['apresentacao'], ENT_QUOTES);

        $this->db->where('id_item', $data['id_item']);
        $this->db->where('atributo', $data['atributo']);
        //$this->db->like('apresentacao', $apresentacao, 'after');

        $query = $this->db->get($this->_table, 1);

        if ($query->num_rows()) 
        {
            $row = $query->row();

            return $row;
        }
        
        return null;
    }

    // }}
    // {{ insert_attr()

    /**
     * Metodo responsavel por inserir um atributo.
     * 
     * <AUTHOR> Santana       <<EMAIL>>
     * 
     * @param  array         $data Os valores a serem atualizados/inseridos. Deve seguir a mesma nomenclatura
     *                             de colunas e estrutura da tabela.
     * 
     * @return int                 O id da nova linha inserida.
     */
    public function insert_attr($data)
    {
        $this->db->insert($this->_table, $data);
        return $this->db->insert_id();
    }

    // }}
    // {{ update_attr()

    /**
     * Metodo responsavel por atualizar um atributo.
     * 
     * <AUTHOR> Santana       <<EMAIL>>
     * 
     * @param  array         $data Os valores a serem atualizados/inseridos. Deve seguir a mesma nomenclatura
     *                             de colunas e estrutura da tabela.
     * 
     * @return int                 O id atualizado.
     */
    public function update_attr($data)
    {
        $id = $data["id"];

        if ($this->get_state('filter.vinculacao')) {
            $this->db->where('(codigo IS NULL OR codigo = "")', NULL, FALSE);
        }

        $this->db->update($this->_table, $data, ["id" => $id]);
        return $id;
    }

    // }}
    // {{ value_has_changed()

    /**
     * Verifica se um valor de uma coluna foi alterado.
     * 
     * <AUTHOR> Santana           <<EMAIL>>
     * 
     * @param  array         $data     Os valores a serem comparados. Deve seguir a mesma nomenclatura de
     *                                 colunas e estrutura da tabela.
     * @param  array         $col_name O nome da coluna verificada.
     * 
     * @return bool                    Se o valor foi alterado ou não.
     */
    public function value_has_changed($data, $col_name)
    {
        if (empty($data["id"])) {
            return FALSE;
        }

        $this->db->where("id", $data["id"]);

        $query = $this->db->get($this->_table);
        
        if (!$query->num_rows()) {
            return FALSE;
        }

        $row = \json_decode(\json_encode($query->row()), TRUE); // Conversão para array.
        
        return $row[$col_name] != $data[$col_name]? TRUE : FALSE;
    }

    /**
     * Deleta os filhos de um atributo root (que não possui pai).
     * <AUTHOR> Santana       <<EMAIL>>
     * @param int          $id_item      O ID do item alvo.
     * @param string       $atributo_pai O atributo pai.
     * @return bool        Se o valor foi deletado ou não pela funcão nativa do code igniter.
     */
    public function delete_childs($id_item, $atributo_pai)
    {
        if (empty($id_item) || empty($atributo_pai)) {
            throw new \Exception("Nao foi possivel realizar a remocao para o atributo em questao.");
        }

        return $this->db->delete($this->_table, [
            "id_item"      => $id_item,
            "atributo_pai" => $atributo_pai
        ]);
    }

    // }}
    // {{ has_attr_empty()

    /**
     * Verifica se possui um atributo vazio.
     * 
     * <AUTHOR>              <<EMAIL>>
     * 
     * @param  array $arr_id_item O ID dos itens a serem verificados.
     * 
     * @return bool               Se é vazio.
     */
    public function has_attr_empty($arr_id_item) 
    {
        if (empty($arr_id_item)) {
            return FALSE;
        }

        if (!\is_array($arr_id_item)) {
            $arr_id_item = array($arr_id_item);
        }

        $this->db->where("codigo IS NOT NULL", NULL, FALSE);
        $this->db->where_in("id_item", $arr_id_item);

        return !empty($this->db->count_all_results($this->_table)) ? TRUE : FALSE;
    }

    // }}
    // {{ has_attr()

    /**
     * Verifica se possui um atributo.
     * 
     * <AUTHOR>              <<EMAIL>>
     * 
     * @param  array $arr_id_item O ID dos itens a serem verificados.
     * 
     * @return bool               Se possui um atributo.
     */
    public function has_attr($arr_id_item) 
    {
        if (empty($arr_id_item)) {
            return FALSE;
        }

        if (!\is_array($arr_id_item)) {
            $arr_id_item = array($arr_id_item);
        }

        $this->db->where_in("id_item", $arr_id_item);

        return !empty($this->db->count_all_results($this->_table)) ? TRUE : FALSE;
    }

    // }}
    // {{ has_attr_empty_by_pn()

    /**
     * Verifica se possui um atributo vazio pelo part number.
     * 
     * <AUTHOR>         <<EMAIL>>
     * 
     * @param  array $arr_pn Os part numbers a serem verificados.
     * 
     * @return bool          Se é vazio.
     */
    public function has_attr_empty_by_pn($arr_pn) 
    {
        if (empty($arr_pn)) {
            return FALSE;
        }

        if (!\is_array($arr_pn)) {
            $arr_pn = array($arr_pn);
        }

        $this->db->where_in("part_numbers", $arr_pn);
        $this->db->where("codigo", "");
        $this->db->where("obrigatorio", "1");

        $this->db->join("cad_item c", "c.id_item = cad_item_attr.id_item", "join");

        return !empty($this->db->count_all_results($this->_table)) ? TRUE : FALSE;
    }

    // }}
    // {{ get_attr()

    /**
     * Realiza a requisição por um atributo com base no ID do item.
     * 
     * <AUTHOR>          <<EMAIL>>
     * 
     * @param  array $id_item O ID do item alvo.
     * @param  bool  $empty   Se deve consultar somente os atributos vazios.
     * 
     * @return bool|object    Se é vazio ou se possuir o atributo.
     */
    public function get_attr($id_item, $empty = FALSE)
    {
        if (empty($id_item)) {
            return FALSE;
        }

        if ($empty) {
            $this->db->where("codigo", "");
        }

        $this->db->where("id_item", $id_item);

        $query = $this->db->get($this->_table);

        if (!$query->num_rows()) {
            return FALSE;
        }

        return $query->result();
    }

    public function get_attr_multiple($id_item, $empty = FALSE)
    {
        if (empty($id_item)) {
            return FALSE;
        }

        if ($empty) {
            $this->db->where("codigo", "");
        }

        $this->db->where_in("id_item", $id_item);
        $query = $this->db->get($this->_table);

        if (!$query->num_rows()) {
            return FALSE;
        }

        return $query->result();
    }

    /**
     * Check for mandatory child attributes of an item.
     *
     * This function checks if there are any mandatory child attributes
     * for a given item that have a NULL or empty 'codigo' field.
     *
     * @param int $id_item The ID of the item to check.
     * @return int The count of mandatory child attributes with empty 'codigo'.
     */
    public function check_mandatory_child_attributes($id_item)
    {
        if (empty($id_item)) {
            return false;
        }

        $this->db->where('id_item', $id_item);
        $this->db->where('atributo_pai IS NOT NULL', null, false);
        // Agrupa a condição para "codigo" ser vazio ou nulo
        $this->db->where("(`codigo` = '' OR `codigo` IS NULL)", null, false);
        $this->db->where('obrigatorio', 1);

        $query = $this->db->get($this->_table);

        if (!$query->num_rows()) {
            return false;
        }

        return $query->result();
    }


    
}