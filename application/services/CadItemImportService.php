<?php

/**
 * @property CI_Loader $load
 * @property Item_model $item_model
 */
class CadItemImportService
{
    /**
     * Instância do CodeIgniter
     *
     * @var CI
     */
    protected $CI;

    public function __construct()
    {
        $this->CI = &get_instance();
        $this->CI->load->model([
            'item_model',
            'empresa_model',
            'cad_item_model',
            'usuario_model',
            'grupo_tarifario_model',
            'cad_item_homologacao_model',
            'lessin_model',
            'item_log_model',
            'cest_model',
            'item_cest_model',
            'cad_item_nve_model',
            'cad_item_attr_model'
        ]);
    }

    /**
     * Processa uma linha da planilha de homologação.
     * @param array $row Linha da planilha (array de valores)
     * @param array $idx Índices das colunas
     * @param array $colunas_originais Nome original das colunas
     * @param int $linha_planilha Número da linha na planilha
     * @param object $empresa Objeto da empresa
     * @param array $funcoes_adicionais Funções adicionais da empresa
     * @param bool $validar Se deve validar o item
     * @return array ['status' => ..., 'logs' => [...], 'erros' => [...], ...]
     * @throws Exception
     */
    public function processRow(
        $row, 
        $idx, 
        $colunas_originais, 
        $linha_planilha, 
        $empresa, 
        $funcoes_adicionais,
        $validar,
        &$logs
    )
    {
        $dbdata = [];
        $erros = [];

        $force = (isset($idx['forcar_atualizacao']) ? mb_strtolower(trim($row[$idx['forcar_atualizacao']])) : 'nao');
        $can_formatar_texto = in_array("formatar_texto", $funcoes_adicionais);

        $part_number = isset($idx['part_number']) ?
            clean_str($row[$idx['part_number']], true) :
            null;

        $estabelecimento = isset($idx['estabelecimento']) ?
            $row[$idx['estabelecimento']] :
            $empresa->estabelecimento_default;

        $estabelecimento = preg_replace('/[\xA0]/u', '', trim($estabelecimento));
        $estabelecimento = convert_accented_characters($estabelecimento);


        $item_importado_default = $this->CI->empresa_model->check_imported_item_permission($empresa->id_empresa);

        // Responsável Fiscal
        $resultFiscal = $this->processarResponsavelFiscal(
            $row,
            $idx,
            $colunas_originais,
            $linha_planilha,
            $empresa,
            $logs,
            $part_number,
            $estabelecimento
        );

        $dbdata = array_merge($dbdata, $resultFiscal['dbdata']);

        // Responsável Engenharia
        $resultEngenharia = $this->processarResponsavelEngenharia(
            $row,
            $idx,
            $colunas_originais,
            $linha_planilha,
            $empresa,
            $logs,
            $part_number,
            $estabelecimento
        );

        $dbdata = array_merge($dbdata, $resultEngenharia['dbdata']);

        // 1. Buscar item
        $id_empresa = $empresa->id_empresa;

        $item = $this->CI->item_model->get_entry($part_number, $id_empresa, $estabelecimento);

        if (empty($item)) {
            $logs['com_erro'][] = [
                'linha' => $linha_planilha,
                'coluna' => $colunas_originais['part_number'] ?? 'Part Number',
                'campo' => 'part_number',
                'mensagem' => 'Part Number inexistente na base',
                'part_number' => $part_number,
                'estabelecimento' => $estabelecimento
            ];
            return [
                'status' => 'erro',
                'logs' => $logs,
                'erros' => $erros
            ];
        }

        // 2. Montar dbdata inicial
        $dbdata['id_empresa'] = $id_empresa;
        $dbdata['part_number'] = $part_number;
        $dbdata['estabelecimento'] = $estabelecimento;

        // 3. Grupo tarifário
        if (isset($idx['grupo_tarifario'])) {
            $grupoTarifarioResult = $this->processarGrupoTarifario(
                $row,
                $idx,
                $colunas_originais,
                $linha_planilha,
                $empresa,
                $funcoes_adicionais,
                $dbdata,
                $erros
            );

            $gt = $grupoTarifarioResult['grupo_tarifario'] ?? null;
        }
        

        if (!empty($grupoTarifarioResult['erros'])) {
            $logs['com_erro'][] = $grupoTarifarioResult['erros'];
            return;
        }

        // 4. Processar campos adicionais
        $camposAdicionais = $this->processarCamposAdicionais(
            $row,
            $idx,
            $colunas_originais,
            $linha_planilha,
            $empresa,
            $funcoes_adicionais,
            $dbdata,
            $erros,
            $gt
        );

        $novoPeso = $camposAdicionais['novoPeso'] ?? null;
        $novaLista = $camposAdicionais['novoLista'] ?? null;
        $novaPrioridade = $camposAdicionais['novoPrioridade'] ?? null;
        $novaMaquina = $camposAdicionais['novoMaquina'] ?? null;
        $novaOrigem = $camposAdicionais['novoOrigem'] ?? null;
        $novasObservacoes = $camposAdicionais['novasObservacoes'] ?? null;
        $gestao_mensal = $camposAdicionais['gestao_mensal'] ?? null;
        $forcar_homologacao = $camposAdicionais['forcar_homologacao'] ?? null;
        $has_descricao_proposta_completa = $camposAdicionais['has_descricao_proposta_completa'] ?? null;
        $dbdata_item = $camposAdicionais['dbdata_item'] ?? null;

        // 5. Verificar se o item já existe na tabela cad_item
        $existe_cad_item = $this->verificarItemExistente($part_number, $id_empresa, $estabelecimento);
        $cad_item_total_rows = $existe_cad_item ? $existe_cad_item->num_rows() : 0;

        $dados_ncm_li_ex_suframa = $this->processarNcmLiExSuframa(
            $row,
            $idx,
            $colunas_originais,
            $linha_planilha,
            $part_number,
            $id_empresa,
            $estabelecimento,
            $dbdata,
            $erros,
            $cad_item_total_rows,
            $existe_cad_item
        );

        $ncm_recomendada = $dados_ncm_li_ex_suframa['ncm_recomendada'] ?? null;
        $ex_ii = $dados_ncm_li_ex_suframa['ex_ii'] ?? null;
        $ex_ipi = $dados_ncm_li_ex_suframa['ex_ipi'] ?? null;
        $li = $dados_ncm_li_ex_suframa['li'] ?? null;
        $suframa = $dados_ncm_li_ex_suframa['suframa'] ?? null;

        // Criar usuário de sessão no sql para armazenar o id_usuario pois algumas triggers precisam dele
        $this->CI->cad_item_model->set_user_id_sql();

        // 6. Atualizar cad_item existente
        if ($cad_item_total_rows > 0) {
            $item_row = $existe_cad_item->row();
            $dados_sistema = $existe_cad_item->result();

            // Atualiza status de exportação
            $this->CI->cad_item_model->atualiza_status_exportacao(0, [$item_row->id_item], $id_empresa);

            // Validação do grupo tarifário
            if (empty($item_row->id_grupo_tarifario)) {
                $logs['com_erro'][] = $this->addErrorLog(
                    $part_number,
                    $estabelecimento,
                    "Item não possui grupo tarifário.",
                    $linha_planilha,
                    $idx['grupo_tarifario'] ?? 'Grupo Tarifário'
                );
                return $this->finalizaComErro($erros, $logs);
            }

            // Importado
            $this->processarImportado(
                $row,
                $idx,
                $part_number,
                $estabelecimento,
                $id_empresa,
                $item_importado_default
            );

            // Status de implementação
            if ($item_row->status_implementacao != 'R' && $item_row->status_implementacao != 'N') {
                $logs['com_erro'][] = $this->addErrorLog(
                    $part_number,
                    $estabelecimento,
                    "Status de implementação não permitido para atualização.",
                    $linha_planilha,
                    $idx['status_implementacao'] ?? 'Status de Implementação'
                );

                return $this->finalizaComErro($erros, $logs);
            } else {
                $this->processarGrupoTarifarioItemExistente(
                    $row,
                    $idx,
                    $colunas_originais,
                    $linha_planilha,
                    $empresa,
                    $funcoes_adicionais,
                    $dbdata,
                    $erros,
                    $gt,
                    $item_row,
                    $ex_ii,
                    $ex_ipi
                );

                $return_nve = $this->CI
                    ->cad_item_nve_model
                    ->salvar_nve_planilha_homologacao(
                        $ncm_recomendada,
                        $row,
                        $idx,
                        $item_row->id_item
                    );

                if (isset($return_nve->error) && $return_nve->error === TRUE) {
                    $logs['com_erro'][] = [
                        'linha' => $linha_planilha,
                        'coluna' => $colunas_originais['nve'] ?? 'NVE',
                        'campo' => 'nve',
                        'mensagem' => $return_nve->message,
                        'part_number' => $part_number,
                        'estabelecimento' => $estabelecimento
                    ];
                    return $this->finalizaComErro($erros, $logs);
                }

                $query_homolog = $this->CI->cad_item_homologacao_model->get_entries_by_id_item($item_row->id_item);

                // Atualização de campos, logs e status
                $this->processarAtualizacaoItemExistente(
                    $row,
                    $idx,
                    $colunas_originais,
                    $linha_planilha,
                    $part_number,
                    $id_empresa,
                    $estabelecimento,
                    $item_row,
                    $dados_sistema,
                    $dbdata,
                    $logs,
                    $erros,
                    $funcoes_adicionais,
                    $ncm_recomendada,
                    $ex_ii,
                    $ex_ipi,
                    $li,
                    $suframa,
                    $return_nve,
                    $gt,
                    $novoPeso,
                    $novaLista,
                    $novaPrioridade,
                    $novaMaquina,
                    $novaOrigem,
                    $novasObservacoes,
                    $gestao_mensal
                );

                if ($query_homolog->num_rows() > 0) {
                    if (
                        isset($gt) && !empty($item_row->part_number_similar) &&
                        isset($item_row->id_grupo_tarifario) &&
                        $item_row->id_grupo_tarifario != $id_grupo_tarifario
                    ) {
                        $erros[] = [
                            'linha' => $linha_planilha,
                            'coluna' => $colunas_originais['grupo_tarifario'] ?? 'Grupo Tarifário',
                            'campo' => 'grupo_tarifario',
                            'mensagem' => 'Para mudar o grupo tarifário do item, é necessário atribuir a foto de um item do mesmo grupo tarifário ao qual ele está sendo importado.'
                        ];
                    }

                    $old_item = $this->CI->cad_item_model->get_entry($item_row->id_item);

                    if (isset($dbdata['cod_cest']) && !empty($dbdata['cod_cest'])) {
                        if ($dbdata['cod_cest'] != $old_item->cod_cest_proposto) {
                            $cest = $this->CI->cest_model->get_entry($dbdata['cod_cest']);
                            $this->registrarLogCest($part_number, $estabelecimento, 'vincular', $cest);
                        }
                    } else {
                        if (!empty($old_item->cod_cest_proposto) && $old_item->cod_cest_proposto) {
                            $this->registrarLogCest($part_number, $estabelecimento, 'desvincular');
                        }
                    }

                    $this->CI->db->update('cad_item', $dbdata, ['id_item' => $item_row->id_item]);
                    $logs_atualizados[] = [
                        'mensagem' => "Part number [<b>{$part_number}</b>][<b>{$estabelecimento}</b>] processado com sucesso.",
                        'part_number' => $part_number,
                        'estabelecimento' => $estabelecimento
                    ];


                    if (isset($idx['forcar_homologacao'])) {
                        $forcar = mb_strtolower(trim($row[$idx['forcar_homologacao']]));

                        if ($forcar == 'sim') {
                            $forcar_homologacao = TRUE;
                        }
                    }

                    if ($forcar_homologacao) {
                        $dbdata2 = array(
                            'part_number'       => $part_number,
                            'estabelecimento'   => $estabelecimento,
                            'tipo_homologacao'  => 'Engenharia',
                            'id_usuario'        => sess_user_id(),
                            'id_empresa'        => $id_empresa,
                            'titulo'            => 'reenvio',
                            'criado_em'         => date("Y-m-d H:i:s"),
                            'motivo'            => 'Item disponibilizado para homologação'
                        );

                        $this->CI->item_log_model->save($dbdata2);
                    }
                } else {
                    $old_item = $this->CI->cad_item_model->get_entry($item_row->id_item);

                    if (isset($dbdata['cod_cest']) && !empty($dbdata['cod_cest'])) {
                        if ($dbdata['cod_cest'] != $old_item->cod_cest_proposto) {
                            $cest = $this->CI->cest_model->get_entry($dbdata['cod_cest']);
                            $this->registrarLogCest($part_number, $estabelecimento, 'vincular', $cest);
                        }
                    } else {
                        if (!empty($old_item->cod_cest_proposto) && $old_item->cod_cest_proposto) {
                            $this->registrarLogCest($part_number, $estabelecimento, 'desvincular');
                        }
                    }

                    if ($force == "sim") {

                        $this->CI->db->update('cad_item', $dbdata, ['id_item' => $item_row->id_item]);
                    }

                    $logs_atualizados[] = [
                        'part_number' => $part_number,
                        'estabelecimento' => $estabelecimento,
                        'mensagem' => "Part number [<b>{$part_number}</b>][<b>{$estabelecimento}</b>] processado com sucesso."
                    ];

                    $forcar_homologacao = false;

                    if (isset($idx['forcar_homologacao'])) {
                        $forcar = mb_strtolower(trim($row[$idx['forcar_homologacao']]));

                        if ($forcar == 'sim') {
                            $forcar_homologacao = true;
                        }
                    }

                    if ($forcar_homologacao) {
                        $dbdata2 = array(
                            'part_number'       => $part_number,
                            'estabelecimento'   => $estabelecimento,
                            'tipo_homologacao'  => 'Engenharia',
                            'id_usuario'        => sess_user_id(),
                            'id_empresa'        => $id_empresa,
                            'titulo'            => 'reenvio',
                            'criado_em'         => date("Y-m-d H:i:s"),
                            'motivo'            => 'Item disponibilizado para homologação'
                        );

                        $this->CI->item_log_model->save($dbdata2);
                    }
                }
            }

            $homologar = false;

            if (!empty($idx['grupo_tarifario'])) {

                if ($item_row->slug == 'homologar') {
                    $this->CI->db->where('id_item', $item_row->id_item);
                    $query = $this->CI->db->get('cad_item_homologacao');

                    if ($query->num_rows() > 0) {
                        $homologar = true;
                    }
                } elseif (
                    $item_row->slug == 'homologado' ||
                    $item_row->slug == 'nao_homologado' ||
                    $item_row->slug == 'revisao' ||
                    $item_row->slug == 'em_analise' ||
                    $item_row->slug == 'pendente_duvidas' ||
                    $item_row->slug == 'respondido'
                ) {
                    $homologar = true;
                }
            }

            if (
                isset($item_row) && $item_row->slug != 'obsoleto' && (
                    (empty($item_row->descricao_mercado_local) &&
                        isset($idx['descricao_proposta_resumida']) &&
                        !empty($row[$idx['descricao_proposta_resumida']]))
                    || (isset($item_row->descricao_mercado_local) &&
                        isset($idx['descricao_proposta_resumida']) &&
                        ($item_row->descricao_mercado_local != $row[$idx['descricao_proposta_resumida']]))
                    || (isset($dados_sistema) &&
                        isset($id_resp_engenharia) &&
                        (reset($dados_sistema)->id_resp_engenharia != $id_resp_engenharia))
                    || (isset($dados_sistema) &&
                        isset($id_resp_fiscal) &&
                        (reset($dados_sistema)->id_resp_fiscal != $id_resp_fiscal))
                    || (!empty($gt) &&
                        $gt->id_grupo_tarifario != $dados_sistema[0]->id_grupo_tarifario)
                )
            ) {
                $homologar = true;
            }

            if (
                isset($gt, $dados_sistema, $gt->id_grupo_tarifario)
                && $gt->id_grupo_tarifario != $dados_sistema[0]->id_grupo_tarifario
            ) {
                $homologar = true;
            }

            if ($forcar_homologacao == true) {
                $homologar = true;

                $log = array(
                    'part_number'       => $dbdata['part_number'],
                    'estabelecimento'   => $dbdata['estabelecimento'],
                    'tipo_homologacao'  => 'Engenharia',
                    'id_usuario'        => sess_user_id(),
                    'id_empresa'        => $id_empresa,
                    'titulo'            => 'envio',
                    'criado_em'         => date("Y-m-d H:i:s"),
                    'motivo'            => '<strong> Envio forçado para homologação via flag </strong>' . '<br>'
                );
                $this->CI->item_log_model->save($log);
            }

            $forcar_mudanca = false;
            if (isset($idx['forcar_homologacao'])) {
                $forcar = mb_strtolower(trim($row[$idx['forcar_homologacao']]));

                if ($forcar == 'sim') {
                    $forcar_mudanca = true;
                }
            }

            $novo_grupo_tarifario = (
                !empty($gt) &&
                !empty($dados_sistema[0]) &&
                $gt->id_grupo_tarifario != $dados_sistema[0]->id_grupo_tarifario
            ) ? true : false;

            if ($homologar == true || $forcar_mudanca == true) {

                $this->atualizarStatus(
                    $item,
                    $dbdata['part_number'],
                    $dbdata['estabelecimento'],
                    $id_empresa,
                    $forcar_mudanca,
                    'atualizar',
                    $item_row->id_item,
                    $homologar,
                    $novo_grupo_tarifario
                );

                if ($novo_grupo_tarifario)
                {
                    $grupo    = $this->CI->grupo_tarifario_model->get_entry($gt->id_grupo_tarifario);
             
                    $motivo = '<strong>Part Number:</strong> ' . 
                    $part_number . '<br><strong>Estabelecimento: </strong>' . 
                    $estabelecimento . '<br> <strong>Grupo atribuído:</strong> ' . 
                    $grupo->descricao . ' <br><small><strong>NCM Recomendada: </strong>' . 
                    $grupo->ncm_recomendada;
    
                    $log_data['titulo'] = 'atribuicaogrupo';
                    $log_data['motivo'] = $motivo;
                    $log_data['part_number'] = $part_number;
                    $log_data['estabelecimento'] = $estabelecimento;
                    $log_data['id_usuario'] = sess_user_id();
                    $log_data['id_empresa'] = sess_user_company();
                    $log_data['criado_em'] = date('Y-m-d H:i:s');
           
                    $this->CI->item_log_model->save($log_data);

                }
            }
        }

        // Processar novo item, caso não exista no cad_item
        if ($cad_item_total_rows == 0 || $existe_cad_item->num_rows() == 0) {

            $id_item = $this->processarNovoItemCadItem(
                $row,
                $idx,
                $colunas_originais,
                $linha_planilha,
                $part_number,
                $id_empresa,
                $estabelecimento,
                $dbdata,
                $logs,
                $erros,
                $item_importado_default,
                $novoPeso,
                $novaLista,
                $novaPrioridade,
                $novaMaquina,
                $novaOrigem,
                $novasObservacoes,
                $has_descricao_proposta_completa,
                $dbdata_item,
                $ncm_recomendada,
                $ex_ii,
                $ex_ipi,
                $suframa
            );

            // Atualiza status
            $forcar_mudanca = false;
            if (isset($idx['forcar_homologacao'])) {
                $forcar = mb_strtolower(trim($row[$idx['forcar_homologacao']]));
                if ($forcar == 'sim') {
                    $forcar_mudanca = true;
                }
            }

            $this->atualizarStatus($item, $part_number, $estabelecimento, $id_empresa, $forcar_mudanca);

            $this->atualizarStatusAtributosItem($id_item, $dbdata['ncm_proposto']);
 
            $grupo    = $this->CI->grupo_tarifario_model->get_entry($gt->id_grupo_tarifario);
            //************************************************************************** */
            $this->CI->cad_item_attr_model->update_cad_item_attr_ncm($part_number, $id_empresa, $estabelecimento, $grupo, $gt->id_grupo_tarifario);
            //
            if ($gt->id_grupo_tarifario)
            {
                $grupo    = $this->CI->grupo_tarifario_model->get_entry($gt->id_grupo_tarifario);
         
                $motivo = '<strong>Part Number:</strong> ' . 
                $part_number . '<br><strong>Estabelecimento: </strong>' . 
                $estabelecimento . '<br> <strong>Grupo atribuído:</strong> ' . 
                $grupo->descricao . ' <br><small><strong>NCM Recomendada: </strong>' . 
                $grupo->ncm_recomendada;

                $log_data['titulo'] = 'atribuicaogrupo';
                $log_data['motivo'] = $motivo;
                $log_data['part_number'] = $part_number;
                $log_data['estabelecimento'] = $estabelecimento;
                $log_data['id_usuario'] = sess_user_id();
                $log_data['id_empresa'] = sess_user_company();
                $log_data['criado_em'] = date('Y-m-d H:i:s');
       
                $this->CI->item_log_model->save($log_data);

            }
        }

        $item_novo_inserido = $cad_item_total_rows == 0 || $existe_cad_item->num_rows() == 0;

        $result = $this->processarAtualizacaoItem($part_number, $estabelecimento, $id_empresa, $row, $idx, $can_formatar_texto, $validar, $force, $item_novo_inserido);
        $dbdata = array_merge($dbdata, $result['dbdata']);

        // Adicionar log de sucesso
        $logs[] = [
            'mensagem' => "Part number [<b>{$part_number}</b>][<b>{$estabelecimento}</b>] processado com sucesso"
        ];

        return [
            'status' => 'ok',
            'logs' => $logs,
            'erros' => $erros,
            'dbdata' => $dbdata,
            'item_base' => $item,
            'campos_adicionais' => $camposAdicionais
        ];
    }

    private function processarResponsavelFiscal(
        $row, 
        $idx, 
        $colunas_originais, 
        $linha_planilha, 
        $empresa,
        &$logs, 
        $part_number, 
        $estabelecimento
    )
    {
        $dbdata = [];
        $erros = [];

        if (isset($idx['id_resp_fiscal'])) {
            $email_resp_fiscal = trim($row[$idx['id_resp_fiscal']]);
            if (!empty($email_resp_fiscal)) {
                $id_resp_fiscal = $this->CI->usuario_model->get_user_id_by_email($email_resp_fiscal);
                if (empty($id_resp_fiscal)) {
                    $logs['warnings'][] = $this->addErrorLog(
                        $part_number,
                        $estabelecimento,
                        "Usuário fiscal não encontrado.",
                        $linha_planilha,
                        $idx['id_resp_fiscal']
                    );
                } else {
                    $dbdata['id_resp_fiscal'] = $id_resp_fiscal;
                }
            } else {
                $logs['warnings'][] = $this->addErrorLog(
                    $part_number,
                    $estabelecimento,
                    "Responsável fiscal vazio.",
                    $linha_planilha,
                    $idx['id_resp_fiscal']
                );
            }
        }

        return ['dbdata' => $dbdata, 'erros' => $erros];
    }

    private function processarResponsavelEngenharia(
        $row, 
        $idx, 
        $colunas_originais, 
        $linha_planilha, 
        $empresa,
        &$logs,
        $part_number,
        $estabelecimento
    )
    {
        $dbdata = [];
        $erros = [];

        if (isset($idx['id_resp_engenharia'])) {
            $email_resp_engenharia = trim($row[$idx['id_resp_engenharia']]);
            if (!empty($email_resp_engenharia)) {
                $id_resp_engenharia = $this->CI->usuario_model->get_user_id_by_email($email_resp_engenharia);
                if (empty($id_resp_engenharia)) {
                    $logs['warnings'][] = $this->addErrorLog(
                        $part_number,
                        $estabelecimento,
                        "Usuário engenheiro não encontrado.",
                        $linha_planilha,
                        $idx['id_resp_engenharia']
                    );
                } else {
                    $dbdata['id_resp_engenharia'] = $id_resp_engenharia;
                }
            } else {
                $logs['warnings'][] = $this->addErrorLog(
                    $part_number,
                    $estabelecimento,
                    "Responsável engenharia vazio.",
                    $linha_planilha,
                    $idx['id_resp_engenharia']
                );
            }
        }

        return ['dbdata' => $dbdata, 'erros' => $erros];
    }

    /**
     * Processa o grupo tarifário.
     * @param array $row Linha da planilha (array de valores)
     * @param array $idx Índices das colunas
     * @param array $colunas_originais Nome original das colunas
     * @param int $linha_planilha Número da linha na planilha
     * @param object $empresa Objeto da empresa
     * @param array $funcoes_adicionais Funções adicionais da empresa
     * @return array ['dbdata' => ..., 'erros' => ...]
     */
    private function processarGrupoTarifario($row, $idx, $colunas_originais, $linha_planilha, $empresa, $funcoes_adicionais, &$dbdata, &$erros)
    {
        $grupo_tarifario_desc = html_entity_decode(trim($row[$idx['grupo_tarifario']]));
        try {
            if (in_array('exibir_id_gpt', $funcoes_adicionais)) {
                $gt = $this->CI->grupo_tarifario_model->get_entry($row[$idx['grupo_tarifario']]);
            } else {
                $gt = $this->CI->grupo_tarifario_model->get_entry_by_desc($grupo_tarifario_desc);
            }
        } catch (Exception $e) {
            $erros['com_erro'][] = [
                'linha' => $linha_planilha,
                'coluna' => $colunas_originais['grupo_tarifario'] ?? 'Grupo Tarifário',
                'campo' => 'grupo_tarifario',
                'mensagem' => 'Grupo tarifário desconhecido',
                'part_number' => $dbdata['part_number'],
                'estabelecimento' => $dbdata['estabelecimento']
            ];
            return [
                'status' => 'erro',
                'erros' => $erros
            ];
        }

        if (empty($gt) || $gt->ativo == 0) {
            $erros['com_erro'][] = [
                'linha' => $linha_planilha,
                'coluna' => $colunas_originais['grupo_tarifario'] ?? 'Grupo Tarifário',
                'campo' => 'grupo_tarifario',
                'mensagem' => 'Grupo tarifário inativo ou não encontrado',
                'part_number' => $dbdata['part_number'],
                'estabelecimento' => $dbdata['estabelecimento']
            ];
            return [
                'status' => 'erro',
                'erros' => $erros
            ];
        }


        if (isset($gt->id_grupo_tarifario)) {
            $dbdata['id_grupo_tarifario'] = $gt->id_grupo_tarifario;
        }
        if (isset($gt->ncm_recomendada)) {
            $dbdata['ncm_proposto'] = $gt->ncm_recomendada;
        }

        return ['dbdata' => $dbdata, 'erros' => $erros, 'grupo_tarifario' => $gt];
    }

    /**
     * Processa os campos adicionais da planilha de homologação.
     * @param array $row Linha da planilha
     * @param array $idx Índices das colunas
     * @param array $colunas_originais Nome original das colunas
     * @param int $linha_planilha Número da linha na planilha
     * @param object $empresa Objeto da empresa
     * @param array $funcoes_adicionais Funções adicionais da empresa
     * @param array &$dbdata Array de dados para atualização/inserção
     * @param array &$erros Array de erros
     * @return array Informações adicionais como novoPeso, novaLista, etc.
     */
    public function processarCamposAdicionais($row, $idx, $colunas_originais, $linha_planilha, $empresa, $funcoes_adicionais, &$dbdata, &$erros, $gt)
    {
        $can_formatar_texto = in_array("formatar_texto", $funcoes_adicionais);
        $campos_adicionais = explode('|', $empresa->campos_adicionais);
        $id_empresa = $empresa->id_empresa;
        $part_number = $dbdata['part_number'];
        $estabelecimento = $dbdata['estabelecimento'];
        $has_descricao_proposta_completa = in_array('descricao_proposta_completa', $campos_adicionais) ?? false;

        $resultado = [
            'has_campo_adicional' => false,
            'novoPeso' => "",
            'novaLista' => "",
            'novaPrioridade' => "",
            'novaMaquina' => "",
            'novaOrigem' => "",
            'novasObservacoes' => "",
            'forcar_homologacao' => false,
            'dbdata_item' => [],
            'has_descricao_proposta_completa' => $has_descricao_proposta_completa
        ];

        // ANTIDUMPING
        if (isset($idx['antidumping']) && !empty($row[$idx['antidumping']])) {
            $dbdata['antidumping'] = $row[$idx['antidumping']];
        }

        // DESCRIÇÃO PROPOSTA RESUMIDA
        if (isset($idx['descricao_proposta_resumida'])) {
            $this->processarDescricaoPropostaResumida(
                $row,
                $idx,
                $colunas_originais,
                $linha_planilha,
                $empresa,
                $can_formatar_texto,
                $dbdata,
                $erros,
                $gt
            );
        }

        // CARACTERÍSTICAS
        if (isset($idx['caracteristicas'])) {
            $this->processarCaracteristicas(
                $row,
                $idx,
                $colunas_originais,
                $linha_planilha,
                $empresa,
                $dbdata,
                $erros,
                $gt
            );
        }

        // SUBSÍDIO
        if (isset($idx['subsidio'])) {
            $this->processarSubsidio(
                $row,
                $idx,
                $colunas_originais,
                $linha_planilha,
                $empresa,
                $dbdata,
                $erros,
                $gt
            );
        }

        // MEMÓRIA DE CLASSIFICAÇÃO
        if (isset($idx['memoria_classificacao'])) {
            $this->processarMemoriaClassificacao(
                $row,
                $idx,
                $colunas_originais,
                $linha_planilha,
                $empresa,
                $dbdata,
                $erros,
                $gt
            );
        }

        // DISPOSITIVO LEGAL
        if (isset($idx['dispositivo_legal'])) {
            $this->processarDispositivoLegal(
                $row,
                $idx,
                $colunas_originais,
                $linha_planilha,
                $empresa,
                $dbdata,
                $erros,
                $gt
            );
        }

        // SOLUÇÃO DE CONSULTA
        if (isset($idx['solucao_consulta'])) {
            $this->processarSolucaoConsulta(
                $row,
                $idx,
                $colunas_originais,
                $linha_planilha,
                $empresa,
                $dbdata,
                $erros,
                $gt
            );
        }

        // DESCRIÇÃO GLOBAL
        if (isset($idx['descricao_global'])) {
            $this->processarDescricaoGlobal(
                $row,
                $idx,
                $part_number,
                $id_empresa,
                $estabelecimento
            );
        }

        // FUNÇÃO
        if (isset($idx['funcao'])) {
            $dbdata['funcao'] = formatar_texto($can_formatar_texto, trim($row[$idx['funcao']]));

            if (in_array('funcao', $campos_adicionais)) {
                $resultado['has_campo_adicional'] = true;
            }
        }

        // OWNER
        if (isset($idx['cod_owner']) && !empty($row[$idx['cod_owner']])) {
            $this->processarOwner(
                $row,
                $idx,
                $colunas_originais,
                $linha_planilha,
                $part_number,
                $id_empresa,
                $estabelecimento,
                $campos_adicionais,
                $erros,
                $resultado
            );
        }

        // PESO
        if (isset($idx['peso'])) {
            $resultado['novoPeso'] = trim($row[$idx['peso']]);


            if (in_array('peso', $campos_adicionais)) {
                $resultado['has_campo_adicional'] = true;
            }
        }

        // LISTA CLIENTE
        if (isset($idx['lista_cliente'])) {
            $lista_cliente = trim($row[$idx['lista_cliente']]);
            if (in_array(strtolower($lista_cliente), array("sim"))) {
                $resultado['novaLista'] = "SIM";
            } elseif (in_array(strtolower($lista_cliente), array("não", "nao"))) {
                $resultado['novaLista'] = "NÃO";
            }
        }

        // GESTÃO MENSAL
        if (isset($idx['gestao_mensal'])) {
            $gestao_mensal = $row[$idx['gestao_mensal']];
            if (in_array($gestao_mensal, array("SIM", "sim", "Sim", "S", "1"))) {
                $resultado['dbdata_item']['gestao_mensal'] = 1;
            } elseif (in_array($gestao_mensal, array("NÃO", "não", "Não", "NAO", "N", "0"))) {
                $resultado['dbdata_item']['gestao_mensal'] = 0;
            }
        }

        // MÁQUINA
        if (isset($idx['maquina'])) {
            $resultado['novaMaquina'] = trim($row[$idx['maquina']]);

            if (in_array('maquina', $campos_adicionais)) {
                $resultado['has_campo_adicional'] = true;
            }
        }

        // ORIGEM
        if (isset($idx['origem'])) {
            $resultado['novaOrigem'] = trim($row[$idx['origem']]);

            if (in_array('origem', $campos_adicionais)) {
                $resultado['has_campo_adicional'] = true;
            }
        }

        // PRIORIDADE
        if (isset($idx['prioridade'])) {
            $resultado['novaPrioridade'] = trim($row[$idx['prioridade']]);

            if (in_array('prioridade', $campos_adicionais)) {
                $resultado['has_campo_adicional'] = true;
            }
        }

        // OBSERVAÇÕES
        if (isset($idx['observacoes'])) {
            $resultado['novasObservacoes'] = trim($row[$idx['observacoes']]);

            if (in_array('observacoes', $campos_adicionais)) {
                $resultado['has_campo_adicional'] = true;
            }
        }

        // INFORMAÇÕES ADICIONAIS
        if (isset($idx['inf_adicionais'])) {
            $dbdata['inf_adicionais'] = formatar_texto($can_formatar_texto, trim($row[$idx['inf_adicionais']]));

            if (in_array('inf_adicionais', $campos_adicionais)) {
                $resultado['has_campo_adicional'] = true;
            }
        }

        // APLICAÇÃO
        if (isset($idx['aplicacao'])) {
            $dbdata['aplicacao'] = formatar_texto($can_formatar_texto, trim($row[$idx['aplicacao']]));

            if (in_array('aplicacao', $campos_adicionais)) {
                $resultado['has_campo_adicional'] = true;
            }
        }

        // MARCA
        if (isset($idx['marca'])) {
            $dbdata['marca'] = formatar_texto($can_formatar_texto, trim($row[$idx['marca']]));

            if (in_array('marca', $campos_adicionais)) {
                $resultado['has_campo_adicional'] = true;
            }
        }

        // MATERIAL CONSTITUTIVO
        if (isset($idx['material_constitutivo'])) {
            $dbdata['material_constitutivo'] = isset($row[$idx['material_constitutivo']]) ?
                formatar_texto($can_formatar_texto, trim($row[$idx['material_constitutivo']])) : null;

            if (in_array('material_constitutivo', $campos_adicionais)) {
                $resultado['has_campo_adicional'] = true;
            }
        }

        // DESCRIÇÃO PROPOSTA COMPLETA
        if (isset($idx['descricao_proposta_completa'])) {
            $resultado['dbdata_item']['descricao_proposta_completa'] =
                formatar_texto($can_formatar_texto, trim($row[$idx['descricao_proposta_completa']]));

            if (in_array('descricao_proposta_completa', $campos_adicionais)) {
                $resultado['has_campo_adicional'] = true;
            }
        }

        // FORÇAR HOMOLOGAÇÃO
        if (isset($idx['forcar_homologacao'])) {
            $forcar = mb_strtolower(trim($row[$idx['forcar_homologacao']]));

            if ($forcar == 'sim') {
                $resultado['forcar_homologacao'] = true;
            }
        }

        // CÓDIGO CEST
        if (isset($idx['cod_cest'])) {
            $this->processarCodigoCest(
                $row,
                $idx,
                $colunas_originais,
                $linha_planilha,
                $part_number,
                $estabelecimento,
                $dbdata,
                $erros
            );
        }

        // STATUS SIMPLUS
        if ($empresa->integracao_simplus == 1 && isset($idx['status_simplus'])) {
            $status_simplus = mb_strtolower(trim($row[$idx['status_simplus']]));

            if (strcmp($status_simplus, 'sim') == 0) {
                $dbdata['status_simplus'] = 0;
            }
        }

        // Data disponível homologação
        if (!empty($dbdata['descricao_mercado_local'])) {
            $dbdata['dat_disp_homologacao'] = date('Y-m-d H:i:s');
        }

        return $resultado;
    }

    /**
     * Processa a descrição proposta resumida.
     */
    private function processarDescricaoPropostaResumida($row, $idx, $colunas_originais, $linha_planilha, $empresa, $can_formatar_texto, &$dbdata, &$erros)
    {
        $part_number = $dbdata['part_number'];
        $estabelecimento = $dbdata['estabelecimento'];
        $id_empresa = $empresa->id_empresa;
        $descricao_proposta_resumida = formatar_texto($can_formatar_texto, trim($row[$idx['descricao_proposta_resumida']]));

        if (empty($descricao_proposta_resumida)) {
            if (isset($gt)) {
                // Tipo de atualização for automática (diferente de Manual)
                if ($gt->tipo_descricao_resumida == NULL) {
                    $erros[] = [
                        'linha' => $linha_planilha,
                        'coluna' => $colunas_originais['descricao_proposta_resumida'] ?? 'Descrição Proposta Resumida',
                        'campo' => 'descricao_erro',
                        'mensagem' => 'Descrição proposta resumida vazia'
                    ];
                } else if ($gt->tipo_descricao_resumida_id == 1) {
                    $erros[] = [
                        'linha' => $linha_planilha,
                        'coluna' => $colunas_originais['descricao_proposta_resumida'] ?? 'Descrição Proposta Resumida',
                        'campo' => 'descricao_erro_grupo_manual',
                        'mensagem' => 'Descrição proposta resumida vazia e grupo tarifário manual'
                    ];
                } else if ($gt->tipo_descricao_resumida_id == 4 && empty($gt->descricao_resumida)) {
                    $erros[] = [
                        'linha' => $linha_planilha,
                        'coluna' => $colunas_originais['descricao_proposta_resumida'] ?? 'Descrição Proposta Resumida',
                        'campo' => 'grupo_descricao_automatica_vazia',
                        'mensagem' => 'Descrição automática do grupo tarifário vazia'
                    ];
                } else {
                    $dbdata['descricao_mercado_local'] = formatar_texto(
                        $can_formatar_texto,
                        $this->CI->grupo_tarifario_model->create_descricao_resumida($part_number, $id_empresa, $gt->id_grupo_tarifario)
                    );
                    $dbdata['forma_descricao_sugerida'] = $gt->tipo_descricao_resumida_id;
                }
            } else {
                $dbdata['descricao_mercado_local'] = formatar_texto($can_formatar_texto, $descricao_proposta_resumida);
            }
        } else {
            $max_chars = $empresa->descricao_max_caracteres;

            if (isset($row['descricao_proposta_resumida']) && !empty($max_chars) && strlen($row['descricao_proposta_resumida']) > $max_chars) {
                $erros[] = [
                    'linha' => $linha_planilha,
                    'coluna' => $colunas_originais['descricao_proposta_resumida'] ?? 'Descrição Proposta Resumida',
                    'campo' => 'descricao_maximo_de_caracteres',
                    'mensagem' => 'Descrição excede o máximo de caracteres permitido'
                ];
            } else {
                $dbdata['descricao_mercado_local'] = formatar_texto($can_formatar_texto, $descricao_proposta_resumida);
            }
        }
    }

    /**
     * Processa o código CEST.
     */
    private function processarCodigoCest($row, $idx, $colunas_originais, $linha_planilha, $part_number, $estabelecimento, &$dbdata, &$erros)
    {
        $cod_cest = isset($row[$idx['cod_cest']]) ? trim($row[$idx['cod_cest']]) : null;
        $dbdata['cod_cest'] = null;

        if (!empty($cod_cest)) {
            if (preg_match("/(?:N\/D\s+?(?:\-|–))?(?:\s+)?N(?:ã|a|A|Ã)o\s+atende/i", $cod_cest)) {
                $dbdata['cod_cest'] = '-1';
            } else {
                if ($this->CI->cest_model->get_entry($cod_cest)) {
                    $dbdata['cod_cest'] = $cod_cest;
                } else {
                    $erros[] = [
                        'linha' => $linha_planilha,
                        'coluna' => $colunas_originais['cod_cest'] ?? 'Código CEST',
                        'campo' => 'cest_erro',
                        'mensagem' => 'Código CEST inválido'
                    ];
                }
            }
        }
    }

    /**
     * Processa o owner.
     */
    private function processarOwner($row, $idx, $colunas_originais, $linha_planilha, $part_number, $id_empresa, $estabelecimento, $campos_adicionais, &$erros, &$resultado)
    {
        $this->CI->load->model('owner_model');
        $this->CI->load->model('item_model');
        $owner = trim($row[$idx['cod_owner']]);
        $force = (isset($idx['forcar_atualizacao']) ? mb_strtolower(trim($row[$idx['forcar_atualizacao']])) : 'nao');

        $codigo_owner_dbdata = $this->CI->owner_model->get_owner($owner);
        if (in_array('owner', $campos_adicionais)) {
            $resultado['has_campo_adicional'] = true;
        }

        if ($force == 'sim') {
            if (empty($codigo_owner_dbdata)) {
                $erros[] = [
                    'linha' => $linha_planilha,
                    'coluna' => $colunas_originais['cod_owner'] ?? 'Código Owner',
                    'campo' => 'owner_inexistente',
                    'mensagem' => 'Código owner inexistente'
                ];
            } else {
                $atualizaOwner = array(
                    'cod_owner' => $codigo_owner_dbdata->codigo
                );
                $this->CI->item_model->update_item(
                    $part_number,
                    $id_empresa,
                    $atualizaOwner,
                    false,
                    $estabelecimento
                );
            }
        }
    }

    /**
     * Processa a descrição global.
     */
    private function processarDescricaoGlobal($row, $idx, $part_number, $id_empresa, $estabelecimento)
    {
        $this->CI->load->model('item_model');

        $descricao_global = trim($row[$idx['descricao_global']]);
        $estabelecimentoItem = $row[$idx['estabelecimento']];

        $atualizaItem = array(
            'descricao_global' => $descricao_global
        );

        $this->CI->item_model->update_item($part_number, $id_empresa, $atualizaItem, false, $estabelecimentoItem);
    }

    /**
     * Verifica se um item existe na tabela cad_item e retorna seus dados
     *
     * @param string $part_number Part number do item
     * @param int $id_empresa ID da empresa
     * @param string $estabelecimento Estabelecimento
     * @return object|false Objeto com dados do item ou false se não existir
     */
    public function verificarItemExistente($part_number, $id_empresa, $estabelecimento)
    {
        $this->CI->load->model('cad_item_model');

        // Verifica se o item existe
        $existe = $this->CI->cad_item_model->check_item_exists($part_number, $id_empresa, $estabelecimento);

        if (!$existe) {
            return false;
        }

        // Busca os dados completos do item
        $this->CI->db->select(
            'ci.id_item,
            ci.part_number,
            ci.descricao_mercado_local,
            ci.subsidio,
            ci.caracteristicas,
            ci.memoria_classificacao,
            ci.houve_descricao_manual,
            ci.dispositivo_legal,
            ci.solucao_consulta,
            ci.funcao,
            ci.inf_adicionais,
            ci.aplicacao,
            ci.suframa_destaque,
            ci.num_ex_ii,
            ci.num_ex_ipi,
            ci.suframa_produto,
            ci.suframa_descricao,
            ci.suframa_codigo,
            ci.suframa_ppb,
            ci.inf_adicionais,
            ci.li,
            ci.li_orgao_anuente,
            ci.li_destaque,
            ci.antidumping,
            ci.houve_inf_adicionais_manual,
            ci.marca,
            ci.material_constitutivo,
            ci.houve_funcao_manual,
            ci.houve_aplicacao_manual,
            ci.houve_marca_manual,
            ci.houve_material_constitutivo_manual,
            ci.estabelecimento,
            ci.status_implementacao,
            ci.motivo_implementacao,
            ci.id_resp_fiscal,
            ci.id_resp_engenharia,
            gt.descricao as desc_grupo_tarifario,
            ci.id_grupo_tarifario,
            i.part_number_similar,
            i.peso,
            i.prioridade,
            i.observacoes,
            i.descricao_global,
            i.descricao_proposta_completa,
            i.houve_descricao_completa_manual,
            i.lista_cliente,
            i.evento,
            i.maquina,
            i.origem,
            s.slug,
            i.gestao_mensal'
        );

        $this->CI->db->join('item i', 'i.part_number = ci.part_number AND i.id_empresa = ci.id_empresa AND i.estabelecimento = ci.estabelecimento', 'left');
        $this->CI->db->join('grupo_tarifario gt', 'gt.id_grupo_tarifario = ci.id_grupo_tarifario', 'left');
        $this->CI->db->join('status s', 's.id=i.id_status', 'inner');

        $query = $this->CI->db->get_where(
            'cad_item ci',
            array(
                'ci.part_number' => $part_number,
                'ci.id_empresa'  => $id_empresa,
                'ci.estabelecimento' => $estabelecimento
            )
        );

        if ($query->num_rows() > 0) {
            return $query;
        }

        return false;
    }

    /**
     * Processa as características do item.
     * @param array $row Linha da planilha
     * @param array $idx Índices das colunas
     * @param array $colunas_originais Nome original das colunas
     * @param int $linha_planilha Número da linha na planilha
     * @param object $empresa Objeto da empresa
     * @param array &$dbdata Array de dados para atualização/inserção
     * @param array &$erros Array de erros
     * @param object $gt Objeto do grupo tarifário
     */
    private function processarCaracteristicas($row, $idx, $colunas_originais, $linha_planilha, $empresa, &$dbdata, &$erros, $gt = null)
    {
        $id_empresa = $empresa->id_empresa;
        $part_number = $dbdata['part_number'];
        $estabelecimento = $dbdata['estabelecimento'];

        $caracteristicas = trim($row[$idx['caracteristicas']]);

        if (empty($caracteristicas)) {
            if (isset($gt)) {
                $this->CI->load->model('grupo_tarifario_excecao_model');
                if ($excecao = $this->CI->grupo_tarifario_excecao_model->get_excecao_empresa('caracteristica', $id_empresa, $gt->id_grupo_tarifario)) {
                    $dbdata['caracteristicas'] = $excecao->descricao;
                } else if (!empty($gt->caracteristica)) {
                    $dbdata['caracteristicas'] = $gt->caracteristica;
                } else {
                    $erros[] = [
                        'linha' => $linha_planilha,
                        'coluna' => $colunas_originais['caracteristicas'] ?? 'Característica',
                        'campo' => 'caracteristica_erro',
                        'mensagem' => 'Característica vazia e não encontrada no grupo tarifário'
                    ];
                }
            } else {
                $dbdata['caracteristicas'] = $caracteristicas;
            }
        } else {
            $dbdata['caracteristicas'] = $caracteristicas;
        }
    }

    /**
     * Processa o subsídio do item.
     * @param array $row Linha da planilha
     * @param array $idx Índices das colunas
     * @param array $colunas_originais Nome original das colunas
     * @param int $linha_planilha Número da linha na planilha
     * @param object $empresa Objeto da empresa
     * @param array &$dbdata Array de dados para atualização/inserção
     * @param array &$erros Array de erros
     * @param object $gt Objeto do grupo tarifário
     */
    private function processarSubsidio($row, $idx, $colunas_originais, $linha_planilha, $empresa, &$dbdata, &$erros, $gt = null)
    {
        $id_empresa = $empresa->id_empresa;
        $part_number = $dbdata['part_number'];
        $estabelecimento = $dbdata['estabelecimento'];

        $subsidio = trim($row[$idx['subsidio']]);

        if (empty($subsidio)) {
            if (isset($gt)) {
                $this->CI->load->model('grupo_tarifario_excecao_model');
                if ($excecao = $this->CI->grupo_tarifario_excecao_model->get_excecao_empresa('subsidio', $id_empresa, $gt->id_grupo_tarifario)) {
                    $dbdata['subsidio'] = $excecao->descricao;
                } elseif (!empty($gt->subsidio)) {
                    $dbdata['subsidio'] = $gt->subsidio;
                } else {
                    $erros[] = [
                        'linha' => $linha_planilha,
                        'coluna' => $colunas_originais['subsidio'] ?? 'Subsídio',
                        'campo' => 'subsidio_erro',
                        'mensagem' => 'Subsídio vazio e não encontrado no grupo tarifário'
                    ];
                }
            } else {
                $dbdata['subsidio'] = $subsidio;
            }
        } else {
            $dbdata['subsidio'] = $subsidio;
        }
    }

    /**
     * Processa a memória de classificação do item.
     * @param array $row Linha da planilha
     * @param array $idx Índices das colunas
     * @param array $colunas_originais Nome original das colunas
     * @param int $linha_planilha Número da linha na planilha
     * @param object $empresa Objeto da empresa
     * @param array &$dbdata Array de dados para atualização/inserção
     * @param array &$erros Array de erros
     * @param object $gt Objeto do grupo tarifário
     */
    private function processarMemoriaClassificacao($row, $idx, $colunas_originais, $linha_planilha, $empresa, &$dbdata, &$erros, $gt = null)
    {
        $id_empresa = $empresa->id_empresa;
        $part_number = $dbdata['part_number'];
        $estabelecimento = $dbdata['estabelecimento'];

        $memoria_classificacao = trim($row[$idx['memoria_classificacao']]);
        $dbdata['memoria_classificacao'] = $memoria_classificacao;

        if (empty($memoria_classificacao) && isset($gt)) {
            $this->CI->load->model('grupo_tarifario_excecao_model');
            if ($excecao = $this->CI->grupo_tarifario_excecao_model->get_excecao_empresa('memoria_classificacao', $id_empresa, $gt->id_grupo_tarifario)) {
                $dbdata['memoria_classificacao'] = $excecao->descricao;
            } else if (!empty($gt->memoria_classificacao)) {
                $dbdata['memoria_classificacao'] = $gt->memoria_classificacao;
            }
        }
    }

    /**
     * Processa o dispositivo legal do item.
     * @param array $row Linha da planilha
     * @param array $idx Índices das colunas
     * @param array $colunas_originais Nome original das colunas
     * @param int $linha_planilha Número da linha na planilha
     * @param object $empresa Objeto da empresa
     * @param array &$dbdata Array de dados para atualização/inserção
     * @param array &$erros Array de erros
     * @param object $gt Objeto do grupo tarifário
     */
    private function processarDispositivoLegal($row, $idx, $colunas_originais, $linha_planilha, $empresa, &$dbdata, &$erros, $gt = null)
    {
        $id_empresa = $empresa->id_empresa;
        $part_number = $dbdata['part_number'];
        $estabelecimento = $dbdata['estabelecimento'];

        $dispositivo_legal = trim($row[$idx['dispositivo_legal']]);
        $dbdata['dispositivo_legal'] = $dispositivo_legal;

        if (empty($dispositivo_legal) && isset($gt)) {
            $this->CI->load->model('grupo_tarifario_excecao_model');
            if ($excecao = $this->CI->grupo_tarifario_excecao_model->get_excecao_empresa('dispositivo_legal', $id_empresa, $gt->id_grupo_tarifario)) {
                $dbdata['dispositivo_legal'] = $excecao->descricao;
            } else if (!empty($gt->dispositivo_legal)) {
                $dbdata['dispositivo_legal'] = $gt->dispositivo_legal;
            }
        }
    }

    /**
     * Processa a solução de consulta do item.
     * @param array $row Linha da planilha
     * @param array $idx Índices das colunas
     * @param array $colunas_originais Nome original das colunas
     * @param int $linha_planilha Número da linha na planilha
     * @param object $empresa Objeto da empresa
     * @param array &$dbdata Array de dados para atualização/inserção
     * @param array &$erros Array de erros
     * @param object $gt Objeto do grupo tarifário
     */
    private function processarSolucaoConsulta($row, $idx, $colunas_originais, $linha_planilha, $empresa, &$dbdata, &$erros, $gt = null)
    {
        $id_empresa = $empresa->id_empresa;
        $part_number = $dbdata['part_number'];
        $estabelecimento = $dbdata['estabelecimento'];

        $solucao_consulta = trim($row[$idx['solucao_consulta']]);
        $dbdata['solucao_consulta'] = $solucao_consulta;

        if (empty($solucao_consulta) && isset($gt)) {
            $this->CI->load->model('grupo_tarifario_excecao_model');
            if ($excecao = $this->CI->grupo_tarifario_excecao_model->get_excecao_empresa('solucao_consulta', $id_empresa, $gt->id_grupo_tarifario)) {
                $dbdata['solucao_consulta'] = $excecao->descricao;
            } else if (!empty($gt->solucao_consulta)) {
                $dbdata['solucao_consulta'] = $gt->solucao_consulta;
            }
        }
    }

    private function processarNcmLiExSuframa(
        $row,
        $idx,
        $colunas_originais,
        $linha_planilha,
        $part_number,
        $id_empresa,
        $estabelecimento,
        &$dbdata,
        &$erros,
        $cad_item_total_rows,
        $existe_cad_item
    ) {
        $this->CI->load->model(['ncm_model', 'ex_tarifario_model', 'suframa_model']);

        $li = null;
        $ex_ii = null;
        $ex_ipi = null;
        $ncm_recomendada = null;
        $suframa = null;

        // NCM recomendada
        if (isset($dbdata['ncm_proposto']) && !empty($dbdata['ncm_proposto'])) {
            $ncm_recomendada = $dbdata['ncm_proposto'];
        } else if ($cad_item_total_rows > 0) {
            if ($existe_cad_item && $existe_cad_item->num_rows() > 0) {
                $row_ncm = $existe_cad_item->row();
                $ncm_recomendada = isset($row_ncm->ncm_proposto) && !empty($row_ncm->ncm_proposto) ? $row_ncm->ncm_proposto : null;
            }
        }

        // Limpar LI se necessário
        if (
            $cad_item_total_rows > 0 &&
            isset($idx['grupo_tarifario']) &&
            !empty($row[$idx['grupo_tarifario']]) &&
            !empty($ncm_recomendada)
        ) {
            $li = $this->CI->ncm_model->get_li_oracle($ncm_recomendada);
            // $li = ''; // para testes pois estamos sem acesso ao oracle local
            if (empty($li)) {
                $dbdata_li = [
                    'li' => 'NÃO',
                    'li_orgao_anuente' => '',
                    'li_destaque' => ''
                ];
                $this->CI->db->where('part_number', $part_number);
                $this->CI->db->where('id_empresa', $id_empresa);
                $this->CI->db->where('estabelecimento', $estabelecimento);
                $this->CI->db->update('cad_item', $dbdata_li);
            }
        }

        // EX-II e EX-IPI
        $ex_ii = null;
        $ex_ipi = null;
        if ((isset($idx['ex_ii']) || isset($idx['ex_ipi'])) && !empty($ncm_recomendada)) {
            if (isset($idx['ex_ii']) && !empty($row[$idx['ex_ii']])) {
                $ex_ii = $this->CI->ex_tarifario_model->get_ex_ii_by_ncm($row[$idx['ex_ii']], $ncm_recomendada);
                if (isset($ex_ii->num_ex) && !empty($ex_ii->num_ex)) {
                    $dbdata['num_ex_ii'] = $ex_ii->num_ex;
                } else {
                    $erros[] = [
                        'linha' => $linha_planilha,
                        'coluna' => $colunas_originais['ex_ii'] ?? 'EX-II',
                        'campo' => 'ex_ii',
                        'mensagem' => 'EX-II inexistente para o NCM informado'
                    ];
                }
            }
            if (isset($idx['ex_ipi']) && !empty($row[$idx['ex_ipi']]) && !empty($ncm_recomendada)) {
                $ex_ipi = $this->CI->ex_tarifario_model->get_ex_ipi_by_ncm($row[$idx['ex_ipi']], $ncm_recomendada);
                if (isset($ex_ipi->num_ex) && !empty($ex_ipi->num_ex)) {
                    $dbdata['num_ex_ipi'] = $ex_ipi->num_ex;
                } else {
                    $erros[] = [
                        'linha' => $linha_planilha,
                        'coluna' => $colunas_originais['ex_ipi'] ?? 'EX-IPI',
                        'campo' => 'ex_ipi',
                        'mensagem' => 'EX-IPI inexistente para o NCM informado'
                    ];
                }
            }
        }

        // LI
        if (isset($idx['li']) && !empty($row[$idx['li']]) && $row[$idx['li']] == 'SIM' && !empty($ncm_recomendada)) {
            $li = $this->CI->ncm_model->getLiOracleParams(
                $ncm_recomendada,
                $row[$idx['li_orgao_anuente']] ?? null,
                $row[$idx['li_destaque']] ?? null
            );
            // $li = ''; // para testes pois estamos sem acesso ao oracle local
            if (!empty($li) && !empty($li->ORGAO_ANUENTE)) {
                $dbdata['li'] = 'SIM';
                $dbdata['li_destaque'] = $row[$idx['li_destaque']] ?? '';
                $dbdata['li_orgao_anuente'] = $li->ORGAO_ANUENTE;
            } else {
                $erros[] = [
                    'linha' => $linha_planilha,
                    'coluna' => $colunas_originais['li'] ?? 'LI',
                    'campo' => 'li',
                    'mensagem' => 'LI inexistente para o NCM informado'
                ];
            }
        }

        // SUFRAMA
        $suframa = ''; // para testes pois estamos sem acesso ao oracle
        if (isset($idx['suframa_descricao']) && !empty($row[$idx['suframa_descricao']]) && !empty($ncm_recomendada)) {
            $suframa = $this->CI->suframa_model->getSuframaByDesc(
                $ncm_recomendada,
                $row[$idx['suframa_descricao']],
                $row[$idx['suframa_codigo']] ?? null
            );
            if (!empty($suframa) && !empty($suframa->CODIGO) && !empty($suframa->DESTAQUE)) {
                $dbdata['suframa_destaque'] = $suframa->DESTAQUE;
                $dbdata['suframa_ppb'] = $suframa->PPB;
                $dbdata['suframa_descricao'] = $suframa->DESCRICAO_SUFRAMA;
                $dbdata['suframa_produto'] = $suframa->PRODUTO;
                $dbdata['suframa_codigo'] = $suframa->CODIGO;
            } else {
                $erros[] = [
                    'linha' => $linha_planilha,
                    'coluna' => $colunas_originais['suframa_descricao'] ?? 'SUFRAMA',
                    'campo' => 'suframa',
                    'mensagem' => 'SUFRAMA inexistente para o NCM informado'
                ];
            }
        }

        // Retorne o ncm_recomendada, ex_ii, ex_ipi, li, suframa
        return [
            'ncm_recomendada' => $ncm_recomendada,
            'ex_ii' => $ex_ii,
            'ex_ipi' => $ex_ipi,
            'li' => $li,
            'suframa' => $suframa
        ];
    }

    private function processarImportado($row, $idx, $part_number, $estabelecimento, $id_empresa, $item_importado_default)
    {
        $this->CI->load->model('cad_item_model');
        if (!isset($idx['importado']) && $item_importado_default == true) {
            $this->CI->cad_item_model->define_item_importado($part_number, $estabelecimento, $id_empresa);
        } else if (isset($idx['importado']) && strtoupper($row[$idx['importado']]) == 'SIM') {
            $this->CI->cad_item_model->define_item_importado($part_number, $estabelecimento, $id_empresa);
        } else if (isset($idx['importado']) && (strtoupper($row[$idx['importado']]) == 'NAO' || strtoupper($row[$idx['importado']]) == 'NÃO')) {
            $this->CI->cad_item_model->remove_item_importado($part_number, $id_empresa, $estabelecimento);
        }
    }

    /**
     * Processa a atualiza o de um item existente no cadastro
     * 
     * @param array $row Linha do arquivo CSV a ser processada
     * @param array $idx Array com os ndices das colunas do arquivo CSV
     * @param array $colunas_originais Array com as colunas originais do arquivo CSV
     * @param int $linha_planilha N mero da linha do arquivo CSV
     * @param string $part_number Part number do item
     * @param int $id_empresa ID da empresa
     * @param string $estabelecimento Estabelecimento
     * @param object $item_row Objeto com os dados do item
     * @param array $dados_sistema Array com os dados do sistema
     * @param array $dbdata Array com os dados a serem atualizados no banco
     * @param array $logs Array com os logs a serem gerados
     * @param array $erros Array com os erros a serem gerados
     * @param array $funcoes_adicionais Array com as fun es adicionais a serem executadas
     * @param string $ncm_recomendada NCM recomendada do item
     * @param object $ex_ii Objeto com os dados do EX-II
     * @param object $ex_ipi Objeto com os dados do EX-IPI
     * @param object $li Objeto com os dados do LI
     * @param object $suframa Objeto com os dados do SUFRAMA
     * @param object $return_nve Objeto com os dados do NVE
     * @param object $gt Objeto com os dados do Grupo Tarifário
     * @param string $novoPeso Novo peso do item
     * @param string $novaLista Nova lista do item
     * @param string $novaPrioridade Nova prioridade do item
     * @param string $novaMaquina Nova máquina do item
     * @param string $novaOrigem Nova origem do item
     * @param string $novasObservacoes Novas observações do item
     */
    private function processarAtualizacaoItemExistente(
        $row,
        $idx,
        $colunas_originais,
        $linha_planilha,
        $part_number,
        $id_empresa,
        $estabelecimento,
        $item_row,
        $dados_sistema,
        &$dbdata,
        &$logs,
        &$erros,
        $funcoes_adicionais,
        $ncm_recomendada,
        $ex_ii,
        $ex_ipi,
        $li,
        $suframa,
        $return_nve,
        $gt,
        $novoPeso,
        $novaLista,
        $novaPrioridade,
        $novaMaquina,
        $novaOrigem,
        $novasObservacoes,
        $gestao_mensal
    ) {
        // Montar motivoLog e motivoItem
        // Atualizar campos do item/cad_item
        // Gerar logs de alteração
        // Atualizar status
        // Validar regras de homologação, forçar homologação, etc

        $motivoLog = '';
        if (isset($dbdata['antidumping']) && !empty($dbdata['antidumping']) && $item_row->antidumping != $dbdata['antidumping']) {
            $motivoLog .= '<strong>ANTIDUMPING: </strong>' . $dbdata['antidumping'] . '<br>';
        }

        if (isset($dbdata['num_ex_ii']) && !empty($dbdata['num_ex_ii']) && $item_row->num_ex_ii != $dbdata['num_ex_ii']) {
            $motivoLog .= '<strong>EX de II: </strong>' . $ex_ii->num_ex . ' - ' . $ex_ii->descricao_linha1 . '<br>';
        }

        if (isset($dbdata['num_ex_ipi']) && !empty($dbdata['num_ex_ipi']) && $item_row->num_ex_ipi != $dbdata['num_ex_ipi']) {
            $motivoLog .= '<strong>EX de IPI: </strong>' . $ex_ipi->num_ex . ' - ' . $ex_ipi->descricao_linha1 . '<br>';
        }

        if (
            isset($dbdata['suframa_destaque']) &&
            !empty($dbdata['suframa_destaque']) &&
            isset($item_row->suframa_destaque) &&
            $item_row->suframa_destaque != $dbdata['suframa_destaque']
        ) {
            $motivoLog .= '<strong>DESTAQUE SUFRAMA: </strong>' . $suframa->DESTAQUE . '<br>';
        }

        if (
            isset($dbdata['suframa_ppb']) &&
            !empty($dbdata['suframa_ppb']) &&
            isset($item_row->suframa_ppb) &&
            $item_row->suframa_ppb != $dbdata['suframa_ppb']
        ) {
            $motivoLog .= '<strong>PPB SUFRAMA: </strong>' . $suframa->PPB . '<br>';
        }

        if (
            isset($dbdata['suframa_descricao']) &&
            !empty($dbdata['suframa_descricao']) &&
            isset($item_row->suframa_descricao) &&
            $item_row->suframa_descricao != $dbdata['suframa_descricao']
        ) {
            $motivoLog .= '<strong>DESCRIÇÃO SUFRAMA: </strong>' . $suframa->DESCRICAO_SUFRAMA . '<br>';
        }

        if (isset($dbdata['suframa_produto']) && !empty($dbdata['suframa_produto']) && $item_row->suframa_produto != $dbdata['suframa_produto']) {
            $motivoLog .= '<strong>PRODUTO SUFRAMA: </strong>' . $suframa->PRODUTO . '<br>';
        }

        if (isset($dbdata['suframa_codigo']) && !empty($dbdata['suframa_codigo']) && $item_row->suframa_codigo != $dbdata['suframa_codigo']) {
            $motivoLog .= '<strong>CÓDIGO SUFRAMA: </strong>' . $suframa->CODIGO . '<br>';
        }

        if (
            isset($dbdata['li']) &&
            !empty($dbdata['li']) &&
            isset($item_row->li_destaque) &&
            $item_row->li_destaque != $dbdata['li_destaque']
        ) {
            if ($dbdata['li'] == 'SIM') {
                $motivoLog .= '<strong>LI: </strong>' . $dbdata['li'] . '<br>';
                $motivoLog .= '<strong>DESTAQUE LI: </strong>' . $dbdata['li_destaque'] . '<br>';
                $motivoLog .= '<strong>ORGÃO ANUENTE: </strong>' . $dbdata['li_orgao_anuente'] . '<br>';
            } else {
                $motivoLog .= '<strong>LI: </strong>' . $dbdata['li'] . '<br>';
            }
        }

        if (isset($return_nve->logs) && !empty($return_nve->logs)) {
            foreach ($return_nve->logs as $log) {
                $motivoLog .= "<strong>NVE [{$log['nve_atributo']}]: </strong>" . $log['nve_valor'] . '<br>';
            }
        }

        $motivoItem = "";

        if ($item_row->peso != $novoPeso && !empty($novoPeso)) {
            $motivoItem = "Alteração do Peso: <em>{$item_row->peso}</em> &rarr; <strong>{$novoPeso}</strong>" . '<br>';
        }

        if (isset($dbdata['caracteristicas']) && !empty($dbdata['caracteristicas']) && $item_row->caracteristicas != $dbdata['caracteristicas']) {
            $motivoItem .= "Alteração da Caracteristica: <em>{$item_row->caracteristicas}</em> &rarr; <strong>{$dbdata['caracteristicas']}</strong>" . '<br>';
        }

        if (isset($dbdata['descricao_mercado_local']) && !empty($dbdata['descricao_mercado_local']) && $item_row->descricao_mercado_local != $dbdata['descricao_mercado_local']) {
            $motivoItem .= "Alteração da Descrição Proposta Resumida: <em>{$item_row->descricao_mercado_local}</em> &rarr; <strong>{$dbdata['descricao_mercado_local']}</strong>" . '<br>';
        }

        if (
            isset($gt) &&
            isset($item_row->id_grupo_tarifario) &&
            ($item_row->id_grupo_tarifario != $gt->id_grupo_tarifario)
        ) {

            $desc_antiga = $this->CI->grupo_tarifario_model->get_entry($item_row->id_grupo_tarifario)->subsidio;
            $desc_atual = $gt->subsidio ?? '';

            $motivoItem .= "Alteração do Grupo: <em>{$desc_antiga}</em> &rarr; <strong>{$desc_atual}</strong>" . '<br>';
        }

        if (isset($dbdata['cod_owner']) && !empty($dbdata['cod_owner']) && $item_row->cod_owner != $dbdata['cod_owner']) {
            $motivoItem .= "Alteração do Owner: <em>{$item_row->cod_owner}</em> &rarr; <strong>{$dbdata['cod_owner']}</strong>" . '<br>';
        }

        if (isset($dbdata['subsidio']) && !empty($dbdata['subsidio']) && $item_row->subsidio != $dbdata['subsidio']) {
            $motivoItem .= "Alteração do Subsidio: <em>{$item_row->subsidio}</em> &rarr; <strong>{$dbdata['subsidio']}</strong>" . '<br>';
        }

        $id_resp_fiscal = $dbdata['id_resp_fiscal'] ?? null;
        $id_resp_engenharia = $dbdata['id_resp_engenharia'] ?? null;

        if (
            isset($dados_sistema) &&
            isset($id_resp_fiscal) &&
            (reset($dados_sistema)->id_resp_fiscal != $id_resp_fiscal)
        ) {
            $desc_antigo = is_array($dados_sistema) ? reset($dados_sistema)->id_resp_fiscal : $dados_sistema->id_resp_fiscal;
            $desc_atual = $id_resp_fiscal;

            $desc_atual = $this->CI->usuario_model->get_email_user($desc_atual);
            $desc_antigo = $this->CI->usuario_model->get_email_user($desc_antigo);

            $motivoItem .= "Alteração do Responsavel Fiscal: <em>{ $desc_antigo }</em> &rarr; <strong>{ $desc_atual }</strong>" . '<br>';
        }

        if (
            isset($dados_sistema) &&
            isset($id_resp_engenharia) &&
            (reset($dados_sistema)->id_resp_engenharia != $id_resp_engenharia)
        ) {
            $desc_antigo = is_array($dados_sistema) ? reset($dados_sistema)->id_resp_engenharia : $dados_sistema->id_resp_engenharia;
            $desc_atual = $id_resp_engenharia;
            $desc_atual = $this->CI->usuario_model->get_email_user($desc_atual);
            $desc_antigo = $this->CI->usuario_model->get_email_user($desc_antigo);

            $motivoItem .= "Alteração do Responsavel de Engenharia: <em>{ $desc_antigo }</em> &rarr; <strong>{ $desc_atual }</strong>" . '<br>';
        }

        if (isset($dbdata['memoria_classificacao']) && !empty($dbdata['memoria_classificacao']) && $item_row->memoria_classificacao != $dbdata['memoria_classificacao']) {
            $motivoItem .= "Alteração da Memória de Classificação: <em>{$item_row->memoria_classificacao}</em> &rarr; <strong>{$dbdata['memoria_classificacao']}</strong>" . '<br>';
        }

        if (isset($dbdata['evento']) && !empty($dbdata['evento']) && $item_row->evento != $dbdata['evento']) {
            $motivoItem .= "Alteração do Evento: <em>{$item_row->evento}</em> &rarr; <strong>{$dbdata['evento']}</strong>" . '<br>';
        }

        if (
            isset($dbdata['dispositivos_legais']) &&
            !empty($dbdata['dispositivos_legais']) &&
            $item_row->dispositivos_legais != $dbdata['dispositivos_legais']
        ) {
            $motivoItem .= "Alteração do Dispositivo Legal: <em>{$item_row->dispositivos_legais}</em> &rarr; <strong>{$dbdata['dispositivos_legais']}</strong>" . '<br>';
        }

        if (
            isset($dbdata['solucao_consulta']) &&
            !empty($dbdata['solucao_consulta']) &&
            $item_row->solucao_consulta != $dbdata['solucao_consulta']
        ) {
            $motivoItem .= "Alteração da Solução de Consulta: <em>{$item_row->solucao_consulta}</em> &rarr; <strong>{$dbdata['solucao_consulta']}</strong>" . '<br>';
        }

        if (isset($dbdata['funcao']) && !empty($dbdata['funcao']) && $item_row->funcao != $dbdata['funcao']) {
            $motivoItem .= "Alteração da Função: <em>{$item_row->funcao}</em> &rarr; <strong>{$dbdata['funcao']}</strong>" . '<br>';
        }

        if (isset($dbdata['peso']) && !empty($dbdata['peso']) && $item_row->peso != $dbdata['peso']) {
            $motivoItem .= "Alteração do Peso: <em>{$item_row->peso}</em> &rarr; <strong>{$dbdata['peso']}</strong>" . '<br>';
        }

        if (isset($dbdata['prioridade']) && !empty($dbdata['prioridade']) && $item_row->prioridade != $dbdata['prioridade']) {
            $motivoItem .= "Alteração de Prioridade: <em>{$item_row->prioridade}</em> &rarr; <strong>{$dbdata['prioridade']}</strong>" . '<br>';
        }

        if (isset($dbdata['inf_adicionais']) && !empty($dbdata['inf_adicionais']) && $item_row->inf_adicionais != $dbdata['inf_adicionais']) {
            $motivoItem .= "Alteração de Informações Adicionais: <em>{$item_row->inf_adicionais}</em> &rarr; <strong>{$dbdata['inf_adicionais']}</strong>" . '<br>';
        }

        if (isset($dbdata['aplicacao']) && !empty($dbdata['aplicacao']) && $item_row->aplicacao != $dbdata['aplicacao']) {
            $motivoItem .= "Alteração de Aplicação: <em>{$item_row->aplicacao}</em> &rarr; <strong>{$dbdata['aplicacao']}</strong>" . '<br>';
        }

        if (isset($dbdata['marca']) && !empty($dbdata['marca']) && $item_row->marca != $dbdata['marca']) {
            $motivoItem .= "Alteração de Marca: <em>{$item_row->marca}</em> &rarr; <strong>{$dbdata['marca']}</strong>" . '<br>';
        }

        if (isset($dbdata['descricao_proposta_completa']) && !empty($dbdata['descricao_proposta_completa']) && $item_row->descricao_proposta_completa != $dbdata['descricao_proposta_completa']) {
            $motivoItem .= "Alteração de Descrição Proposta Completa: <em>{$item_row->descricao_proposta_completa}</em> &rarr; <strong>{$dbdata['descricao_proposta_completa']}</strong>" . '<br>';
        }

        if (isset($dbdata['descricao_proposta_resumida']) && !empty($dbdata['descricao_proposta_resumida']) && $item_row->descricao_mercado_local != $dbdata['descricao_proposta_resumida']) {
            $motivoItem .= "Alteração de Descrição Proposta Resumida: <em>{$item_row->descricao_mercado_local}</em> &rarr; <strong>{$dbdata['descricao_proposta_resumida']}</strong>" . '<br>';
        }

        if (isset($dbdata['material_constitutivo']) && !empty($dbdata['material_constitutivo']) && $item_row->material_constitutivo != $dbdata['material_constitutivo']) {
            $motivoItem .= "Alteração de Material Constitutivo: <em>{$item_row->material_constitutivo}</em> &rarr; <strong>{$dbdata['material_constitutivo']}</strong>" . '<br>';
        }

        if (isset($dbdata['cod_cest']) && !empty($dbdata['cod_cest']) && $item_row->cod_cest != $dbdata['cod_cest']) {
            $motivoItem .= "Alteração de Cest: <em>{$item_row->cod_cest}</em> &rarr; <strong>{$dbdata['cod_cest']}</strong>" . '<br>';
        }

        if (isset($dbdata['status_simplus']) && !empty($dbdata['status_simplus']) && $item_row->status_simplus != $dbdata['status_simplus']) {
            $motivoItem .= "Alteração de Simplus: <em>{$item_row->status_simplus}</em> &rarr; <strong>{$dbdata['status_simplus']}</strong>" . '<br>';
        }

        if (isset($dbdata['estabelecimento']) && !empty($dbdata['estabelecimento']) && $item_row->estabelecimento != $dbdata['estabelecimento']) {
            $motivoItem .= "Alteração de Estabelecimento: <em>{$item_row->estabelecimento}</em> &rarr; <strong>{$dbdata['estabelecimento']}</strong>" . '<br>';
        }

        if (
            isset($novaLista) &&
            $item_row->lista_cliente != $novaLista &&
            !empty($novaLista)
        ) {
            $motivoItem .= "Alteração da Lista: <em>{$item_row->lista_cliente}</em> &rarr; <strong>{$novaLista}</strong>" . '<br>';
        }

        if (
            isset($novaPrioridade) &&
            $item_row->prioridade != $novaPrioridade &&
            !empty($novaPrioridade)
        ) {
            $motivoItem .= !empty($motivoItem) ? "<br/>" : "";
            $motivoItem .= "Alteração da Prioridade: <em>{$item_row->prioridade}</em> &rarr; <strong>{$novaPrioridade}</strong>" . '<br>';
        }

        if (
            isset($novaMaquina) &&
            $item_row->maquina != $novaMaquina &&
            !empty($novaMaquina)
        ) {
            $motivoItem .= !empty($motivoItem) ? "<br/>" : "";
            $motivoItem .= "Alteração da Maquina: <em>{$item_row->maquina}</em> &rarr; <strong>{$novaMaquina}</strong>" . '<br>';
        }

        if (
            isset($novaOrigem) &&
            $item_row->origem != $novaOrigem &&
            !empty($novaOrigem)
        ) {
            $motivoItem .= !empty($motivoItem) ? "<br/>" : "";
            $motivoItem .= "Alteração da Origem: <em>{$item_row->origem}</em> &rarr; <strong>{$novaOrigem}</strong>" . '<br>';
        }

        if (
            isset($novasObservacoes) &&
            $item_row->observacoes != $novasObservacoes &&
            !empty($novasObservacoes)
        ) {
            $motivoItem .= !empty($motivoItem) ? "<br/>" : "";
            $motivoItem .= "Alteração das Observações: <em>{$item_row->observacoes}</em> &rarr;$ <strong>{$novasObservacoes}</strong>" . '<br>';
        }

        // Gestão Mensal
        if (
            isset($gestao_mensal) &&
            isset($item_row->gestao_mensal) &&
            $item_row->gestao_mensal != $gestao_mensal &&
            !empty($gestao_mensal)
        ) {
            $motivoItem .= "Alteração da Gestão Mensal: <em>" . ($item_row->gestao_mensal == 1 ? 'Sim' : 'Não') . "</em> &rarr; <strong>" . ($gestao_mensal == 1 ? 'Sim' : 'Não') . "</strong><br>";
        }

        $itemUpdate = [];

        // LESSIN
        if (!empty($ncm_recomendada) && in_array('lessin', $funcoes_adicionais)) {
            $exII = false;
            if (isset($dbdata['num_ex_ii']) && !empty($dbdata['num_ex_ii'])) {
                if ($item_row->num_ex_ii != $dbdata['num_ex_ii']) {
                    $exII = $dbdata['num_ex_ii'];
                } else {
                    $exII = $item_row->num_ex_ii;
                }
            }

            try {
                $utilizaLessin = $this->CI->lessin_model->utiliza_lessin(array('ncm' => $ncm_recomendada), $exII);

                $motivoItem .= !empty($motivoItem) ? "<br />" : "";

                $itemUpdate['id_lessin'] = $utilizaLessin['id'];
                $itemUpdate['regra_aplicada'] = $utilizaLessin['regra'];
                $itemUpdate['lista_becomex'] = isset($utilizaLessin['utiliza_lessin']) && $utilizaLessin['utiliza_lessin'] ? 'SIM' : 'NÃO';

                $motivoItem .= "Atualização da LESSIN: Lista Becomex de <em>{$item_row->lista_becomex}</em> para <strong>{$itemUpdate['lista_becomex']}</strong>; Lista cliente de <em>{$item_row->lista_cliente}</em> para <strong>{$novaLista}</strong>.";
            } catch (Exception $e) {
                $erros['item_homologado'][] = [
                    'linha' => $linha_planilha,
                    'coluna' => $colunas_originais['lista_becomex'] ?? 'lista_becomex',
                    'part_number' => $dbdata['part_number'],
                    'estabelecimento' => $dbdata['estabelecimento'],
                    'message' => $e->getMessage(),
                ];
            }
        }

        $force = (isset($idx['forcar_atualizacao']) ? mb_strtolower(trim($row[$idx['forcar_atualizacao']])) : 'nao');

        if ($force != "sim") {
            if (isset($idx['descricao_proposta_resumida']) && empty(trim($idx['descricao_proposta_resumida']))) {
                $erros['excecao_erro'][] = [
                    'linha' => $linha_planilha,
                    'coluna' => $colunas_originais['descricao_proposta_resumida'] ?? 'descricao_proposta_resumida',
                    'part_number' => $dbdata['part_number'],
                    'estabelecimento' => $dbdata['estabelecimento'],
                    'message' => 'Descrição da Proposta Resumida está vazia',
                ];
            }
        }

        if (!empty($motivoItem)) {
            if ($force == "sim") {
                $itemUpdate["peso"] = !empty($novoPeso) ? $novoPeso : $item_row->peso;
                $itemUpdate["lista_cliente"] = !empty($novaLista) ? $novaLista : $item_row->lista_cliente;
                $itemUpdate["prioridade"] = !empty($novaPrioridade) ? $novaPrioridade : $item_row->prioridade;
                $itemUpdate["observacoes"] = !empty($novasObservacoes) ? $novasObservacoes : $item_row->observacoes;
                $itemUpdate["maquina"] = !empty($novaMaquina) ? $novaMaquina : $item_row->maquina;
                $itemUpdate["origem"] = !empty($novaOrigem) ? $novaOrigem : $item_row->origem;

                if (!empty($gestao_mensal)) {
                    $itemUpdate["gestao_mensal"] = $gestao_mensal;
                }

                $this->CI->item_model->update_item($dbdata['part_number'], $id_empresa, $itemUpdate, $motivoItem, $dbdata['estabelecimento']);
            }
        }

        if (!empty($motivoLog)) {
            $log = array(
                'part_number'       => $dbdata['part_number'],
                'estabelecimento'   => $dbdata['estabelecimento'],
                'tipo_homologacao'  => 'Engenharia',
                'id_usuario'        => sess_user_id(),
                'id_empresa'        => $id_empresa,
                'titulo'            => 'reenvio',
                'criado_em'         => date("Y-m-d H:i:s"),
                'motivo'            => $motivoLog
            );

            $this->CI->item_log_model->save($log);
        }

        // Atualização no banco
        $this->CI->db->update('cad_item', $dbdata, ['id_item' => $item_row->id_item]);

        // Atualizar status wf dos atributos
        $this->CI->cad_item_model->atualizar_status_atributos($part_number, $id_empresa, $estabelecimento);

        $logs['atualizados'][] = [
            'part_number' => $part_number,
            'estabelecimento' => $estabelecimento,
            'mensagem' => "Part number [<b>{$part_number}</b>][<b>{$estabelecimento}</b>] processado com sucesso."
        ];

        // Geração de log
        if (!empty($motivoLog)) {
            $log = [
                'part_number' => $part_number,
                'estabelecimento' => $estabelecimento,
                'tipo_homologacao' => 'Engenharia',
                'id_usuario' => sess_user_id(),
                'id_empresa' => $id_empresa,
                'titulo' => 'reenvio',
                'criado_em' => date("Y-m-d H:i:s"),
                'motivo' => $motivoLog
            ];
            $this->CI->item_log_model->save($log);
        }
    }

    private function atualizarStatus(
        $item_base,
        $part_number,
        $estabelecimento,
        $id_empresa,
        $forcar_mudanca = false,
        $tipo = 'novos',
        $id_item = null,
        $homologar = false,
        $novo_grupo_tarifario = false
    ) {
        if ($forcar_mudanca == false && $tipo != 'novos') {
            // Não deve haver troca de status de itens já homologados ou reprovados
            if (($item_base->id_status == '2' || $item_base->id_status == '3') && $novo_grupo_tarifario == false) {
                return;
            }
            if ($homologar == false) {
                return;
            }
        }

        $this->CI->load->library("Item/Status");
        $this->CI->status->set_status("homologar");
        $this->CI->status->update_item($part_number, $estabelecimento, $id_empresa);

        if ($tipo != 'novos' && !empty($id_item)) {
            $this->CI->cad_item_homologacao_model->drop_item($id_item);
        }
    }

    private function finalizaComErro($erros = [], $logs)
    {
        if (!empty($erros)) {
            $logs['com_erro'] = array_merge($logs['com_erro'], $erros);
        }

        return [
            'status' => 'erro',
            'logs' => $logs,
            'erros' => $erros
        ];
    }

    private function processarGrupoTarifarioItemExistente(
        $row,
        $idx,
        $colunas_originais,
        $linha_planilha,
        $empresa,
        $funcoes_adicionais,
        &$dbdata,
        $erros,
        $gt,
        $item_row,
        $ex_ii,
        $ex_ipi
    ) {
        if (
            isset($gt) &&
            isset($item_row->id_grupo_tarifario) &&
            ($item_row->id_grupo_tarifario != $gt->id_grupo_tarifario)
        ) {

            $dbdata['num_ex_ii'] = NULL;
            $dbdata['num_ex_ipi'] = NULL;

            if (isset($ex_ii) && !empty($ex_ii)) {
                $dbdata['num_ex_ii'] = $ex_ii->num_ex;
            }

            if (isset($ex_ipi) && !empty($ex_ipi)) {
                $dbdata['num_ex_ipi'] = $ex_ipi->num_ex;
            }

            if (isset($item_row->id_item) && !empty($item_row->id_item)) {
                $this->CI->db->where('id_item', $item_row->id_item);
                $this->CI->db->delete('cad_item_nve');
            }

        }
    }

    private function registrarLogCest($part_number, $estabelecimento, $acao, $cest = null)
    {
        $this->CI->load->model(['item_log_model', 'item_cest_model']);

        $log_data = [
            'part_number'     => $part_number,
            'estabelecimento' => $estabelecimento,
            'id_usuario'      => sess_user_id(),
            'id_empresa'      => sess_user_company(),
            'criado_em'       => date('Y-m-d H:i:s'),
        ];

        if ($acao === 'vincular' && $cest) {
            $log_data['titulo'] = 'vinculacao_cest_novo';
            $descricao = "<strong>CEST:</strong> {$cest->cod_cest}<br /><strong>Descrição:</strong> {$cest->descricao}";
            $motivo_log = '<strong>CEST Vinculado</strong><br />' . $descricao;
        } else {
            $log_data['titulo'] = 'vinculacao_cest_remove';
            $motivo_log = '<strong>CEST desvinculado</strong>';
        }
        $log_data['motivo'] = $motivo_log;

        $this->CI->item_log_model->save($log_data);
        $this->CI->item_cest_model->insert_item_log(
            $part_number,
            sess_user_company(),
            $estabelecimento,
            [],
            $motivo_log
        );
    }

    private function processarNovoItemCadItem(
        $row,
        $idx,
        $colunas_originais,
        $linha_planilha,
        $part_number,
        $id_empresa,
        $estabelecimento,
        &$dbdata,
        &$logs,
        &$erros,
        $item_importado_default,
        $novoPeso,
        $novaLista,
        $novaPrioridade,
        $novaMaquina,
        $novaOrigem,
        $novasObservacoes,
        $has_descricao_proposta_completa,
        $dbdata_item,
        $ncm_recomendada,
        $ex_ii,
        $ex_ipi,
        $suframa
    ) {
        $this->CI->load->model(['item_model', 'cad_item_model', 'cad_item_nve_model', 'empresa_prioridades_model']);

        // Prioridade atual
        $prioridade_atual_item = $this->CI->empresa_prioridades_model
            ->get_old_prioridade(
                $part_number,
                $id_empresa,
                $estabelecimento
            );

        $old_prioridade = $prioridade_atual_item[0]->prioridade_atual ?? null;

        // Salva descrição proposta completa se necessário
        if (
            !empty($dbdata['descricao_mercado_local']) &&
            $has_descricao_proposta_completa === TRUE
        ) {
            $this->CI->item_model->save(
                ['descricao_proposta_completa' => $dbdata_item['descricao_proposta_completa']],
                ['part_number' => $part_number, 'id_empresa' => $id_empresa, 'estabelecimento' => $estabelecimento]
            );
        }

        // Insere na cad_item
        $this->CI->db->insert('cad_item', $dbdata);
        $id_item = $this->CI->db->insert_id();

        // Importado
        $this->processarImportado($row, $idx, $part_number, $estabelecimento, $id_empresa, $item_importado_default);

        $return_nve = $this->CI
                    ->cad_item_nve_model
                    ->salvar_nve_planilha_homologacao(
                        $ncm_recomendada,
                        $row,
                        $idx,
                        $id_item
                    );
                    
        // Salva NVE
        if (isset($return_nve->error) && $return_nve->error === TRUE) {
            $erros[] = [
                'linha' => $linha_planilha,
                'coluna' => $colunas_originais['nve'] ?? 'NVE',
                'campo' => 'nve',
                'mensagem' => 'NVE inexistente para o NCM informado'
            ];
        }

        // Motivo de alteração
        $motivoItem = '';
        if (!empty($novoPeso)) {
            $motivoItem = "Alteração do Peso: <strong>{$novoPeso}</strong>";
        }
        if (!empty($novaLista)) {
            $motivoItem = "Alteração da Lista: <strong>{$novaLista}</strong>";
        }
        if (!empty($novaPrioridade) && $old_prioridade != $novaPrioridade) {
            $motivoItem .= !empty($motivoItem) ? "<br/>" : "";
            $motivoItem .= "Alteração de Prioridade: <em>{$old_prioridade}</em> &rarr; <strong>{$novaPrioridade}</strong><br>";
        }
        if (!empty($novaMaquina)) {
            $motivoItem .= !empty($motivoItem) ? "<br/>" : "";
            $motivoItem .= "Alteração da Maquina: <strong>{$novaMaquina}</strong>";
        }
        if (!empty($novaOrigem)) {
            $motivoItem .= !empty($motivoItem) ? "<br/>" : "";
            $motivoItem .= "Alteração da Origem: <strong>{$novaOrigem}</strong>";
        }
        if (!empty($novasObservacoes)) {
            $motivoItem .= !empty($motivoItem) ? "<br/>" : "";
            $motivoItem .= "Alteração das Observações: <strong>{$novasObservacoes}</strong>";
        }

        // Atualiza item se necessário
        if (!empty($motivoItem)) {
            $this->CI->item_model->update_item($part_number, $id_empresa, [
                "peso" => $novoPeso,
                "lista_cliente" => $novaLista,
                "prioridade" => $novaPrioridade,
                "maquina" => $novaMaquina,
                "origem" => $novaOrigem,
                "observacoes" => $novasObservacoes
            ], $motivoItem, $estabelecimento);
        }

        // Log de inserção
        $dbdata2 = [
            'part_number'       => $part_number,
            'estabelecimento'   => $estabelecimento,
            'tipo_homologacao'  => 'Engenharia',
            'id_usuario'        => sess_user_id(),
            'id_empresa'        => $id_empresa,
            'titulo'            => 'envio',
            'criado_em'         => date("Y-m-d H:i:s"),
            'motivo'            => ''
        ];

        $forcar_homologacao = false;

        // Motivo detalhado
        if (isset($idx['id_resp_engenharia']) || isset($idx['id_resp_fiscal'])) {
            if (isset($idx['forcar_homologacao'])) {
                $forcar = mb_strtolower(trim($row[$idx['forcar_homologacao']]));
                if ($forcar == 'sim') {
                    $forcar_homologacao = TRUE;
                }
            }
            if ($forcar_homologacao) {
                $dbdata2['motivo'] = 'Item disponibilizado para Homologação. <br>';
            }

            $email_resp_fiscal = $row[$idx['id_resp_fiscal']] ?? '';
            if (!empty($email_resp_fiscal)) {
                $dbdata2['motivo'] .= '<strong>Responsável Fiscal: </strong> ' . $email_resp_fiscal . '<br>';
            }

            $email_resp_engenharia = $row[$idx['id_resp_engenharia']] ?? '';
            if (!empty($email_resp_engenharia)) {
                $dbdata2['motivo'] .= '<strong>Responsável Técnico: </strong>' . $email_resp_engenharia . '<br>';
            }
        }
        if (isset($dbdata['num_ex_ii']) && !empty($dbdata['num_ex_ii']) && $ex_ii) {
            $dbdata2['motivo'] .= '<strong>EX de II: </strong>' . $ex_ii->num_ex . ' - ' . $ex_ii->descricao_linha1 . '<br>';
        }
        if (isset($dbdata['num_ex_ipi']) && !empty($dbdata['num_ex_ipi']) && $ex_ipi) {
            $dbdata2['motivo'] .= '<strong>EX de IPI: </strong>' . $ex_ipi->num_ex . ' - ' . $ex_ipi->descricao_linha1 . '<br>';
        }
        if (isset($dbdata['antidumping']) && !empty($dbdata['antidumping'])) {
            $dbdata2['motivo'] .= '<strong>ANTIDUMPING: </strong>' . $dbdata['antidumping'] . '<br>';
        }
        if (isset($dbdata['suframa_destaque']) && !empty($dbdata['suframa_destaque']) && $suframa) {
            $dbdata2['motivo'] .= '<strong>DESTAQUE SUFRAMA: </strong>' . $suframa->DESTAQUE . '<br>';
        }
        if (isset($dbdata['suframa_ppb']) && !empty($dbdata['suframa_ppb']) && $suframa) {
            $dbdata2['motivo'] .= '<strong>PPB SUFRAMA: </strong>' . $suframa->PPB . '<br>';
        }
        if (isset($dbdata['suframa_descricao']) && !empty($dbdata['suframa_descricao']) && $suframa) {
            $dbdata2['motivo'] .= '<strong>DESCRIÇÃO SUFRAMA: </strong>' . $suframa->DESCRICAO_SUFRAMA . '<br>';
        }
        if (isset($dbdata['suframa_produto']) && !empty($dbdata['suframa_produto']) && $suframa) {
            $dbdata2['motivo'] .= '<strong>PRODUTO SUFRAMA: </strong>' . $suframa->PRODUTO . '<br>';
        }
        if (isset($dbdata['suframa_codigo']) && !empty($dbdata['suframa_codigo']) && $suframa) {
            $dbdata2['motivo'] .= '<strong>CÓDIGO SUFRAMA: </strong>' . $suframa->CODIGO . '<br>';
        }
        if (isset($dbdata['li']) && !empty($dbdata['li'])) {
            if ($dbdata['li'] == 'SIM') {
                $dbdata2['motivo'] .= '<strong>LI: </strong>' . $dbdata['li'] . '<br>';
                $dbdata2['motivo'] .= '<strong>DESTAQUE LI: </strong>' . $dbdata['li_destaque'] . '<br>';
                $dbdata2['motivo'] .= '<strong>ORGÃO ANUENTE: </strong>' . $dbdata['li_orgao_anuente'] . '<br>';
            } else {
                $dbdata2['motivo'] .= '<strong>LI: </strong>' . $dbdata['li'] . '<br>';
            }
        }
        if (isset($return_nve->logs) && !empty($return_nve->logs)) {
            foreach ($return_nve->logs as $log) {
                $dbdata2['motivo'] .= "<strong>NVE [{$log['nve_atributo']}]: </strong>" . $log['nve_valor'] . '<br>';
            }
        }

        $this->CI->item_log_model->save($dbdata2);

        $log_inseridos[] = array('part_number' => $part_number, 'estabelecimento' => $estabelecimento);

        // Limpa atributos não relacionados
        if (!empty($dbdata['ncm_proposto']) && !empty($id_item)) {
            $this->atualizarStatusAtributosItem($id_item, $dbdata['ncm_proposto']);

            // Atualiza o wf_status_atributos do item
            $this->CI->cad_item_model->atualizar_status_atributos($part_number, $id_empresa, $estabelecimento);
        }

        // Adiciona log de inserção
        $logs['inseridos'][] = [
            'part_number' => $part_number,
            'estabelecimento' => $estabelecimento,
            'mensagem' => "Part number [<b>{$part_number}</b>][<b>{$estabelecimento}</b>] inserido com sucesso."
        ];

        return $id_item;
    }

    private function atualizarStatusAtributosItem($id_item, $ncm_proposto)
    {
        if (empty($id_item) || empty($ncm_proposto)) {
            return;
        }

        $this->CI->cad_item_model->limpar_atributos_nao_relacionados($id_item, $ncm_proposto, true);


        $query =  $this->CI->db->query("SELECT 
                    (SELECT 
                            COUNT(*)
                        FROM
                            comex cm
                        WHERE
                            ind_ecomex = 'EI'
                                AND cm.part_number_original = i.part_number
                                AND cm.unidade_negocio = i.estabelecimento
                                AND cm.id_empresa = i.id_empresa
                        LIMIT 1) AS tipo_item,
                    ci.part_number,
                    ci.estabelecimento,
                    ci.id_empresa,
                    CASE
                        WHEN
                            EXISTS( SELECT 
                                    1
                                FROM
                                    ncm_atributo n
                                WHERE
                                    n.ncm = ci.ncm_proposto
                                        AND (n.codigo = 'null'))
                        THEN
                            '5'
                    WHEN
                        COUNT(*) = 0
                            OR COUNT(CASE
                            WHEN attr.codigo IS NULL OR attr.codigo = '' THEN 1
                        END) = COUNT(*)
                    THEN
                        '1'
                    WHEN
                        COUNT(CASE
                            WHEN
                                attr.obrigatorio = 1
                                    AND (attr.codigo IS NULL OR attr.codigo = '')
                            THEN
                                1
                        END) > 0
                    THEN
                        '2'
                    WHEN
                        COUNT(CASE
                            WHEN
                                (attr.obrigatorio = 0
                                    AND (attr.codigo IS NULL OR attr.codigo = ''))
                                    OR (attr.id_item IS NULL)
                            THEN
                                1
                            ELSE NULL
                        END) > 0
                    THEN
                        '3'
                    WHEN
                        COUNT(CASE
                            WHEN
                                attr.codigo IS NOT NULL
                                    AND attr.codigo <> ''
                            THEN
                                1
                        END) = COUNT(*)
                    THEN
                        '4'
                    ELSE '0'
                END AS status_preenchimento
            FROM
                cad_item ci
                    LEFT JOIN
                cad_item_attr attr ON ci.id_item = attr.id_item
                    INNER JOIN
                item i ON ci.part_number = i.part_number
                    AND ci.estabelecimento = i.estabelecimento
                    AND ci.id_empresa = i.id_empresa

            WHERE ci.id_item = '{$id_item}'
            GROUP BY ci.id_item");


        while ($item = $query->unbuffered_row()) {
            $this->CI->db->query(" UPDATE item i
            SET i.status_attr = '{$item->status_preenchimento}'
                WHERE i.part_number = '{$item->part_number}' 
                AND i.estabelecimento = '{$item->estabelecimento}'
                AND i.id_empresa = '{$item->id_empresa}'
                AND i.status_attr <> '{$item->status_preenchimento}'");


            if ($item->tipo_item == 0)
            {
                if ($item->status_preenchimento == 3 || $item->status_preenchimento == 4)
                {
                    $this->db->query(" UPDATE item i
                    SET i.wf_status_atributos = '7'
                        WHERE i.part_number = '{$item->part_number}' 
                        AND i.estabelecimento = '{$item->estabelecimento}'
                        AND i.id_empresa = '{$item->id_empresa}'");  
                }

            } 
        }
    }

    private function getItem($part_number, $estabelecimento, $id_empresa) {
        $this->CI->db->select('ci.id_item, i.*');

        $where = array(
            'i.part_number'     => $part_number,
            'i.estabelecimento' => $estabelecimento,
            'i.id_empresa'      => $id_empresa
        );

        $this->CI->db->where($where);
        $this->CI->db->join(
            'cad_item ci',
            'ci.part_number = i.part_number and ci.estabelecimento = i.estabelecimento and ci.id_empresa = i.id_empresa',
            'inner',
            false
        );

        return $this->CI->db->get('item i');
    }

    /**
     * Processa a atualização de um item existente na tabela item
     * 
     * @param string $part_number Part number do item
     * @param string $estabelecimento Estabelecimento
     * @param int $id_empresa ID da empresa
     * @param array $row Linha da planilha
     * @param array $idx Índices das colunas
     * @param bool $can_formatar_texto Flag indicando se pode formatar texto
     * @param bool $validar Flag indicando se deve validar
     * @param string $force Flag indicando se deve forçar atualização
     * @param bool $item_novo_inserido Flag indicando se o item foi recém inserido
     * @return array Resultado do processamento
     */
    private function processarAtualizacaoItem(
        $part_number, 
        $estabelecimento, 
        $id_empresa, 
        $row, 
        $idx, 
        $can_formatar_texto, 
        $validar, 
        $force, 
        $item_novo_inserido = false
    )
    {
        $validar_evento = [];
        $dbdata = [];
        
        $verifica_item = $this->getItem($part_number, $estabelecimento, $id_empresa);
        
        if ($verifica_item->num_rows() > 0) {
            $itemRow = $verifica_item->row();
            
            if ($item_novo_inserido === true) {
                $dbdata['data_envio'] = date('Y-m-d H:i:s');
            }
            
            // Processar descrição proposta completa
            if (isset($idx['descricao_proposta_completa'])) {
                $nova_descricao_completa = formatar_texto($can_formatar_texto, trim($row[$idx['descricao_proposta_completa']]));
                if (
                    $validar !== TRUE
                    && $itemRow->descricao_proposta_completa != $nova_descricao_completa
                ) {
                    $dbupdate_item = array();
                    $dbupdate_item['descricao_proposta_completa'] = $nova_descricao_completa;
                    $dbupdate_item['houve_descricao_completa_manual'] = 0;
                    
                    $descricao_completa_texto = $itemRow->descricao_proposta_completa ? $itemRow->descricao_proposta_completa : '<em>Não informado</em>';
                    $nova_descricao_completa_texto = $nova_descricao_completa ? $nova_descricao_completa : '<em>Não informado</em>';
                    
                    $motivo_item = "Alteração da Descrição proposta completa: " . $descricao_completa_texto . " -> <strong>" . $nova_descricao_completa_texto . "</strong><br />";
                    
                    if ($force == "sim") {
                        $dbdata_item_ret = $this->CI->item_model->update_item($itemRow->part_number, $itemRow->id_empresa, $dbupdate_item, $motivo_item, $itemRow->estabelecimento);
                    }
                    $dbdata['descricao_proposta_completa'] = $nova_descricao_completa;
                }
            }
            
            // Processar informações adicionais
            if (isset($idx['inf_adicionais'])) {
                $inf_adicionais = formatar_texto($can_formatar_texto, trim($row[$idx['inf_adicionais']]));
                if (!empty($inf_adicionais) && isset($itemRow->inf_adicionais) && $inf_adicionais != $itemRow->inf_adicionais)
                    $dbdata['inf_adicionais'] = $inf_adicionais;
            }
            
            // Processar evento
            if (isset($idx['evento'])) {
                $evento = formatar_texto($can_formatar_texto, trim($row[$idx['evento']]));
                
                // Novas regras para o campo 'Evento' (Chamado: #2027)
                if ($validar === TRUE && !empty($itemRow->evento)) {
                    // Caso o campo evento da planilha foi informado e é diferente do encontrado na tabela item
                    if (!empty($evento) && ($itemRow->evento != $evento)) {
                        $validar_evento[] = array(
                            'item'        => $itemRow,
                            'novo_evento' => $evento
                        );
                    }
                } else {
                    // Caso o campo da tabela item esteja vazio
                    if (empty($evento)) {
                        // Será utilizado um evento com nome genérico com base na data atual
                        $current_date = date('Y-m-d');
                        $evento = 'PACOTE ' . $current_date;
                    }
                    
                    // Adiciona no grupo para o update da tabela item
                    $dbdata['evento'] = $evento;
                }
            }
            
            // Atualiza o item se necessário
            if ($force == "sim" && !empty($dbdata)) {
                $this->CI->db->update(
                    'item i', 
                    $dbdata, 
                    ['part_number' => $itemRow->part_number, 'id_empresa' => $itemRow->id_empresa, 'estabelecimento' => $itemRow->estabelecimento]
                );
            }
        }
        
        return [
            'dbdata' => $dbdata,
            'validar_evento' => $validar_evento,
            'item_row' => $verifica_item->num_rows() > 0 ? $verifica_item->row() : null
        ];
    }

    /**
     * Adiciona um erro ao log com informações detalhadas
     *
     * @param string $part_number Número da peça
     * @param string $estabelecimento Estabelecimento
     * @param string $mensagem Mensagem de erro
     * @param int $linha Número da linha (opcional)
     * @param string|int $coluna Nome ou índice da coluna (opcional)
     * @return array Entrada de log formatada
     */
    private function addErrorLog($part_number, $estabelecimento, $mensagem, $linha = null, $coluna = null)
    {
        // Garantir que part_number e estabelecimento nunca sejam vazios no log
        $part_number = !empty($part_number) ? $part_number : 'N/A';
        $estabelecimento = !empty($estabelecimento) ? $estabelecimento : 'N/A';
        
        // Formato detalhado para o relatório
        $log_entry = [
            'part_number' => $part_number,
            'estabelecimento' => $estabelecimento,
            'mensagem' => $mensagem,
            'linha' => $linha,
            'coluna' => $coluna
        ];
        
        return $log_entry;
    }
}
