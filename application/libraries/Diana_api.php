<?php
defined('BASEPATH') || exit('No direct script access allowed');

/**
 * Biblioteca para integração com API DIANA
 * Responsável por fazer chamadas para o serviço de análise de triagem
 */
class Diana_api
{
    private $CI;
    private $api_url;
    private $api_url_description_summary;
    private $api_url_description_detailed;
    private $api_url_description_ncm_detailed;

    public function __construct()
    {
        $this->CI = &get_instance();
        $this->CI->load->library('guzzle');
        $this->api_url = $this->CI->config->item('diana_api_url');
        $this->api_url_description_summary = $this->CI->config->item('diana_api_url_description_summary');
        $this->api_url_description_detailed = $this->CI->config->item('diana_api_url_description_detailed');
        $this->api_url_description_ncm_detailed = $this->CI->config->item('diana_api_url_description_ncm_detailed');
    }

    /**
     * Envia dados para análise DIANA
     *
     * @param string $funcao Função do item
     * @param string $aplicacao Aplicação do item
     * @param string $material_constitutivo Material constitutivo do item
     * @param string $descricao Descrição do item (opcional)
     * @return array|false Resposta da API ou false em caso de erro
     */
    public function analisar_triagem($funcao, $aplicacao, $material_constitutivo, $descricao = '')
    {
        try {
            // Concatenar os campos conforme especificação DIANA usando '+'
            // Permite campos vazios conforme especificado
            $dados_concatenados = trim($descricao . '+' . $funcao . '+' . $aplicacao . '+' . $material_constitutivo);

            // Construir URL com query parameter conforme API espera
            $url_com_parametro = $this->api_url . '?item_descr=' . urlencode($dados_concatenados);

            $this->log_diana('info', 'Enviando dados para análise', [
                'url_length' => strlen($url_com_parametro),
                'item_descr_length' => strlen($dados_concatenados),
                'item_descr' => substr($dados_concatenados, 0, 100) . '...'
            ]);

            $client = new GuzzleHttp\Client([
                'timeout' => 30,
                'connect_timeout' => 10
            ]);

            $response = $client->request('POST', $url_com_parametro, [
                'headers' => [
                    'Accept' => 'application/json'
                ]
            ]);

            $body = $response->getBody()->getContents();
            $data = json_decode($body, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->log_diana('error', 'Erro ao decodificar JSON da resposta', [
                    'json_error' => json_last_error_msg(),
                    'response_body' => substr($body, 0, 500)
                ]);
                return false;
            }

            $this->log_diana('info', 'Resposta recebida com sucesso', [
                'response_size' => strlen($body),
                'elapsed_time' => $data['elapsed_time'] ?? 'não informado'
            ]);

            return $data;
        } catch (GuzzleHttp\Exception\RequestException $e) {
            $context = ['exception_type' => 'RequestException'];

            if ($e->hasResponse()) {
                $response = $e->getResponse();
                $context['status_code'] = $response->getStatusCode();
                $context['response_body'] = substr($response->getBody()->getContents(), 0, 500);
            }

            $this->log_diana('error', 'Erro na requisição HTTP: ' . $e->getMessage(), $context);
            return false;
        } catch (Exception $e) {
            $this->log_diana('error', 'Erro geral na análise: ' . $e->getMessage(), [
                'exception_type' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return false;
        }
    }

    /**
     * Envia dados para análise DIANA incluindo perguntas e respostas
     *
     * @param string $funcao Função do item
     * @param string $aplicacao Aplicação do item
     * @param string $material_constitutivo Material constitutivo do item
     * @param string $descricao Descrição do item
     * @param array $perguntas_respostas Array com perguntas e respostas
     * @return array|false Resposta da API ou false em caso de erro
     */
    public function analisar_triagem_com_respostas(
        $funcao,
        $aplicacao,
        $material_constitutivo,
        $descricao = '',
        $perguntas_respostas = []
    )
    {
        try {
            // Concatenar os campos básicos (descricao, funcao, aplicacao, material_constitutivo) e perguntas e respostas (se houver)
            $dados_concatenados = trim($descricao . '+' . $funcao . '+' . $aplicacao . '+' . $material_constitutivo);

            // Adicionar perguntas e respostas à concatenação (se houver)
            if (!empty($perguntas_respostas)) {
                foreach ($perguntas_respostas as $pr) {
                    if (!empty($pr->pergunta)) {
                        $dados_concatenados .= '+' . $pr->pergunta;
                        if (!empty($pr->resposta)) {
                            $dados_concatenados .= '+' . $pr->resposta;
                        }
                    }
                }
            }

            // Construir URL com query parameter conforme API espera
            $url_com_parametro = $this->api_url . '?item_descr=' . urlencode($dados_concatenados);

            $this->log_diana('info', 'Enviando dados para análise (com respostas)', [
                'url_length' => strlen($url_com_parametro),
                'item_descr_length' => strlen($dados_concatenados),
                'perguntas_respostas_count' => count($perguntas_respostas),
                'item_descr' => substr($dados_concatenados, 0, 150) . '...'
            ]);

            $client = new GuzzleHttp\Client([
                'timeout' => 30,
                'connect_timeout' => 10
            ]);

            $response = $client->request('POST', $url_com_parametro, [
                'headers' => [
                    'Accept' => 'application/json'
                ]
            ]);

            $body = $response->getBody()->getContents();
            $data = json_decode($body, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->log_diana('error', 'Erro ao decodificar JSON da resposta', [
                    'json_error' => json_last_error_msg(),
                    'response_body' => substr($body, 0, 500)
                ]);
                return false;
            }

            $this->log_diana('info', 'Resposta recebida com sucesso (com respostas)', [
                'response_size' => strlen($body),
                'elapsed_time' => $data['elapsed_time'] ?? 'não informado'
            ]);

            return $data;
        } catch (GuzzleHttp\Exception\RequestException $e) {
            $context = ['exception_type' => 'RequestException'];

            if ($e->hasResponse()) {
                $response = $e->getResponse();
                $context['status_code'] = $response->getStatusCode();
                $context['response_body'] = substr($response->getBody()->getContents(), 0, 500);
            }

            $this->log_diana('error', 'Erro na requisição HTTP (com respostas): ' . $e->getMessage(), $context);
            return false;
        } catch (Exception $e) {
            $this->log_diana('error', 'Erro geral na análise (com respostas): ' . $e->getMessage(), [
                'exception_type' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return false;
        }
    }

    /**
     * Processa a resposta da API DIANA
     *
     * @param array $resposta_api Resposta da API DIANA
     * @return array Array com resultado da análise
     */
    public function processar_resposta($resposta_api)
    {
        if (!is_array($resposta_api) || !isset($resposta_api['questions'])) {
            return [
                'sucesso' => false,
                'erro' => 'Resposta da API inválida'
            ];
        }

        $perguntas_faltantes = [];
        $tem_informacao_faltante = false;

        foreach ($resposta_api['questions'] as $pergunta) {
            if (isset($pergunta['falta_informacao']) && $pergunta['falta_informacao'] === true) {
                $tem_informacao_faltante = true;
                $perguntas_faltantes[] = $pergunta['question'];
            }
        }

        return [
            'sucesso' => true,
            'triagem_aprovada' => !$tem_informacao_faltante,
            'perguntas_faltantes' => $perguntas_faltantes,
            'tempo_execucao' => $resposta_api['elapsed_time'] ?? null
        ];
    }

    /**
     * Valida se a URL da API está configurada
     *
     * @return bool
     */
    public function is_configurada()
    {
        return !empty($this->api_url) && filter_var($this->api_url, FILTER_VALIDATE_URL);
    }

    /**
     * Registra log específico para operações DIANA
     *
     * @param string $level Nível do log (error, info, debug)
     * @param string $message Mensagem do log
     * @param array $context Contexto adicional
     */
    private function log_diana($level, $message, $context = [])
    {
        $log_message = "DIANA API: {$message}";

        if (!empty($context)) {
            $log_message .= " - Context: " . json_encode($context);
        }

        log_message($level, $log_message);

        // Log adicional em arquivo específico para DIANA (opcional)
        $this->log_to_file($level, $log_message);
    }

    /**
     * Escreve log em arquivo específico para DIANA
     *
     * @param string $level
     * @param string $message
     */
    private function log_to_file($level, $message)
    {
        try {
            $log_dir = APPPATH . 'logs/';
            $log_path = $log_dir . 'diana-' . date('Y-m-d') . '.log';
            $timestamp = date('Y-m-d H:i:s');
            $log_entry = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;

            // Verificar se o diretório existe, se não, criar
            if (!is_dir($log_dir)) {
                mkdir($log_dir, 0777, true);
            }

            // Verificar se o arquivo existe, se não, criar com permissões adequadas
            if (!file_exists($log_path)) {
                // Criar o arquivo
                file_put_contents($log_path, '', LOCK_EX);
                // Definir permissões 666 (rw-rw-rw-) para permitir escrita por qualquer usuário
                chmod($log_path, 0777);
            }

            file_put_contents($log_path, $log_entry, FILE_APPEND | LOCK_EX);
        } catch (Exception $e) {
            // Se falhar ao escrever no arquivo, apenas ignora
            // para não quebrar o fluxo principal
            // Pode logar no log padrão do CodeIgniter (opcional)
            log_message('error', 'Falha ao escrever log DIANA: ' . $e->getMessage());
        }
    }

    /**
     * Envia dados para DIANA sugerir descrição resumida
     *
     * @param array $itens_api Itens de requisição para DIANA sugerir descrição
     * @param string $tipo Tipo de descrição ('resumida', 'completa' ou 'supercompleta')
     * @return array|false Resposta da API ou false em caso de erro
     */
    public function sugerir_descricao($itens_api, $tipo)
    {
        try {
            switch ($tipo) {
                case 'resumida':
                    $url = $this->api_url_description_summary;
                    break;
                case 'completa':
                    $url = $this->api_url_description_detailed;
                    break;
                case 'supercompleta':
                    $url = $this->api_url_description_ncm_detailed;
                    break;
                default:
                    $this->log_diana('error', 'Tipo de descrição inválido', [
                        'tipo' => $tipo
                    ]);
                    return false;
            }

            $this->log_diana('info', 'Enviando dados para sugerir descrição ' . $tipo, [
                'url_length' => strlen($url),
                'itens_api_count' => count($itens_api),
                'body_length' => strlen(json_encode($itens_api)),
            ]);

            $client = new GuzzleHttp\Client([
                'timeout' => 30,
                'connect_timeout' => 10
            ]);

            $response = $client->request('POST', $url, [
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
                'json' => $itens_api,
            ]);

            $body = $response->getBody()->getContents();
            $data = json_decode($body, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->log_diana('error', 'Erro ao decodificar JSON da resposta (sugerir descrição ' . $tipo . ')', [
                    'json_error' => json_last_error_msg(),
                    'response_body' => substr($body, 0, 500)
                ]);
                return false;
            }

            $this->log_diana('info', 'Resposta recebida com sucesso (sugerir descrição ' . $tipo . ')', [
                'response_size' => strlen($body),
                'elapsed_time' => $data['elapsed_time'] ?? 'não informado'
            ]);

            return $data;
        } catch (GuzzleHttp\Exception\RequestException $e) {
            $context = ['exception_type' => 'RequestException'];

            if ($e->hasResponse()) {
                $response = $e->getResponse();
                $context['status_code'] = $response->getStatusCode();
                $context['response_body'] = substr($response->getBody()->getContents(), 0, 500);
            }

            $this->log_diana('error', 'Erro na requisição HTTP (sugerir descrição ' . $tipo . '): ' . $e->getMessage(), $context);
            return false;
        } catch (Exception $e) {
            $this->log_diana('error', 'Erro geral na sugestão de descrição ' . $tipo . ': ' . $e->getMessage(), [
                'exception_type' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return false;
        }
    }

    /**
     * Valida se as URLs de sugestão de descrição da API está configurada
     *
     * @return bool
     */
    public function is_configurada_sugestao_descricao()
    {
        return !empty($this->api_url_description_summary) && filter_var($this->api_url_description_summary, FILTER_VALIDATE_URL) &&
            !empty($this->api_url_description_detailed) && filter_var($this->api_url_description_detailed, FILTER_VALIDATE_URL) &&
            !empty($this->api_url_description_ncm_detailed) && filter_var($this->api_url_description_ncm_detailed, FILTER_VALIDATE_URL);
    }

}
