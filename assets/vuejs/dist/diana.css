.vld-shown {
  overflow: hidden;
}

.vld-overlay {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  align-items: center;
  display: none;
  justify-content: center;
  overflow: hidden;
  z-index: 9999;
}

.vld-overlay.is-active {
  display: flex;
}

.vld-overlay.is-full-page {
  z-index: 9999;
  position: fixed;
}

.vld-overlay .vld-background {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  background: #fff;
  opacity: 0.5;
}

.vld-overlay .vld-icon, .vld-parent {
  position: relative;
}

.list-group-item[data-v-e85c26]:hover {
    background-color: #F9F9F9;
    cursor: pointer;
}
.fa.fa-grip[data-v-e85c26]:before {
    letter-spacing: 0.1em;
    content: "\f142 \f142";
}
.fa.fa-grip[data-v-e85c26] {
    font-size: 16px;
    color: lightgray;
    margin-right: 10px;
}
.grabbable[data-v-e85c26] {
    cursor: move;
    cursor: grab;
    cursor: -moz-grab;
    cursor: -webkit-grab;
}
.grabbable[data-v-e85c26]:active {
    cursor: grabbing;
    cursor: -moz-grabbing;
    cursor: -webkit-grabbing;
}.mb-2[data-v-ee9ff1] {
    margin-bottom: 10px;
}