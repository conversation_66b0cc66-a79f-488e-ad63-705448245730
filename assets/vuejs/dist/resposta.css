.vld-shown {
  overflow: hidden;
}

.vld-overlay {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  align-items: center;
  display: none;
  justify-content: center;
  overflow: hidden;
  z-index: 9999;
}

.vld-overlay.is-active {
  display: flex;
}

.vld-overlay.is-full-page {
  z-index: 9999;
  position: fixed;
}

.vld-overlay .vld-background {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  background: #fff;
  opacity: 0.5;
}

.vld-overlay .vld-icon, .vld-parent {
  position: relative;
}

.text-primary[data-v-7477d1] {
    color: blue !important;
}
.form-group.files .form-control[data-v-7477d1] {
    padding: 5px 4px;
}
.btn-download[data-v-7477d1] {
    display: block;
    padding: 5px 12px;
    border-bottom: 1px solid rgb(211,211,211);
}
.btn-download[data-v-7477d1]:last-child {
    display: block;
    padding: 5px 12px;
    border-bottom: none;
}
.form-control .input-file[data-v-7477d1] {
    padding: 5px 5px !important;
}
.input-file[data-v-7477d1]{
    width: 200px;
}
.size-textarea[data-v-7477d1]{
    max-width: 750px !important;
    max-height: 80px !important;
}/**
    Support for SASS is deprecated as of v3.18.

    The files remain here if your build is dependent on them
    but they will not receive updates in future releases. All
    SASS variables have been translated into CSS variables, so
    migration should be quite simple if you'd like to move over.

    In v4, these files will be removed.
 */
.v-select {
  position: relative;
  font-family: inherit; }

.v-select,
.v-select * {
  box-sizing: border-box; }

/* KeyFrames */
@-webkit-keyframes vSelectSpinner {
  0% {
    transform: rotate(0deg); }
  100% {
    transform: rotate(360deg); } }

@keyframes vSelectSpinner {
  0% {
    transform: rotate(0deg); }
  100% {
    transform: rotate(360deg); } }

/* Dropdown Default Transition */
.vs__fade-enter-active,
.vs__fade-leave-active {
  pointer-events: none;
  transition: opacity 0.15s cubic-bezier(1, 0.5, 0.8, 1); }

.vs__fade-enter,
.vs__fade-leave-to {
  opacity: 0; }

/** Component States */
/*
 * Disabled
 *
 * When the component is disabled, all interaction
 * should be prevented. Here we modify the bg color,
 * and change the cursor displayed on the interactive
 * components.
 */
.vs--disabled .vs__dropdown-toggle,
.vs--disabled .vs__clear,
.vs--disabled .vs__search,
.vs--disabled .vs__selected,
.vs--disabled .vs__open-indicator {
  cursor: not-allowed;
  background-color: #f8f8f8; }

/*
 *  RTL - Right to Left Support
 *
 *  Because we're using a flexbox layout, the `dir="rtl"`
 *  HTML attribute does most of the work for us by
 *  rearranging the child elements visually.
 */
.v-select[dir="rtl"] .vs__actions {
  padding: 0 3px 0 6px; }

.v-select[dir="rtl"] .vs__clear {
  margin-left: 6px;
  margin-right: 0; }

.v-select[dir="rtl"] .vs__deselect {
  margin-left: 0;
  margin-right: 2px; }

.v-select[dir="rtl"] .vs__dropdown-menu {
  text-align: right; }

/**
    Dropdown Toggle

    The dropdown toggle is the primary wrapper of the component. It
    has two direct descendants: .vs__selected-options, and .vs__actions.

    .vs__selected-options holds the .vs__selected's as well as the
    main search input.

    .vs__actions holds the clear button and dropdown toggle.
 */
.vs__dropdown-toggle {
  appearance: none;
  display: flex;
  padding: 0 0 4px 0;
  background: none;
  border: 1px solid rgba(60, 60, 60, 0.26);
  border-radius: 4px;
  white-space: normal; }

.vs__selected-options {
  display: flex;
  flex-basis: 100%;
  flex-grow: 1;
  flex-wrap: wrap;
  padding: 0 2px;
  position: relative; }

.vs__actions {
  display: flex;
  align-items: center;
  padding: 4px 6px 0 3px; }

/* Dropdown Toggle States */
.vs--searchable .vs__dropdown-toggle {
  cursor: text; }

.vs--unsearchable .vs__dropdown-toggle {
  cursor: pointer; }

.vs--open .vs__dropdown-toggle {
  border-bottom-color: transparent;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0; }

.vs__open-indicator {
  fill: rgba(60, 60, 60, 0.5);
  transform: scale(1);
  transition: transform 150ms cubic-bezier(1, -0.115, 0.975, 0.855);
  transition-timing-function: cubic-bezier(1, -0.115, 0.975, 0.855); }

.vs--open .vs__open-indicator {
  transform: rotate(180deg) scale(1); }

.vs--loading .vs__open-indicator {
  opacity: 0; }

/* Clear Button */
.vs__clear {
  fill: rgba(60, 60, 60, 0.5);
  padding: 0;
  border: 0;
  background-color: transparent;
  cursor: pointer;
  margin-right: 8px; }

/* Dropdown Menu */
.vs__dropdown-menu {
  display: block;
  box-sizing: border-box;
  position: absolute;
  top: calc(100% - 1px);
  left: 0;
  z-index: 1000;
  padding: 5px 0;
  margin: 0;
  width: 100%;
  max-height: 350px;
  min-width: 160px;
  overflow-y: auto;
  box-shadow: 0px 3px 6px 0px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(60, 60, 60, 0.26);
  border-top-style: none;
  border-radius: 0 0 4px 4px;
  text-align: left;
  list-style: none;
  background: #fff; }

.vs__no-options {
  text-align: center; }

/* List Items */
.vs__dropdown-option {
  line-height: 1.42857143;
  /* Normalize line height */
  display: block;
  padding: 3px 20px;
  clear: both;
  color: #333;
  /* Overrides most CSS frameworks */
  white-space: nowrap;
  cursor: pointer; }

.vs__dropdown-option--highlight {
  background: #5897fb;
  color: #fff; }

.vs__dropdown-option--deselect {
  background: #fb5858;
  color: #fff; }

.vs__dropdown-option--disabled {
  background: inherit;
  color: rgba(60, 60, 60, 0.5);
  cursor: inherit; }

/* Selected Tags */
.vs__selected {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  border: 1px solid rgba(60, 60, 60, 0.26);
  border-radius: 4px;
  color: #333;
  line-height: 1.4;
  margin: 4px 2px 0px 2px;
  padding: 0 0.25em;
  z-index: 0; }

.vs__deselect {
  display: inline-flex;
  appearance: none;
  margin-left: 4px;
  padding: 0;
  border: 0;
  cursor: pointer;
  background: none;
  fill: rgba(60, 60, 60, 0.5);
  text-shadow: 0 1px 0 #fff; }

/* States */
.vs--single .vs__selected {
  background-color: transparent;
  border-color: transparent; }

.vs--single.vs--open .vs__selected,
.vs--single.vs--loading .vs__selected {
  position: absolute;
  opacity: .4; }

.vs--single.vs--searching .vs__selected {
  display: none; }

/* Search Input */
/**
 * Super weird bug... If this declaration is grouped
 * below, the cancel button will still appear in chrome.
 * If it's up here on it's own, it'll hide it.
 */
.vs__search::-webkit-search-cancel-button {
  display: none; }

.vs__search::-webkit-search-decoration,
.vs__search::-webkit-search-results-button,
.vs__search::-webkit-search-results-decoration,
.vs__search::-ms-clear {
  display: none; }

.vs__search,
.vs__search:focus {
  appearance: none;
  line-height: 1.4;
  font-size: 1em;
  border: 1px solid transparent;
  border-left: none;
  outline: none;
  margin: 4px 0 0 0;
  padding: 0 7px;
  background: none;
  box-shadow: none;
  width: 0;
  max-width: 100%;
  flex-grow: 1;
  z-index: 1; }

.vs__search::placeholder {
  color: inherit; }

/**
    States
 */
.vs--unsearchable .vs__search {
  opacity: 1; }

.vs--unsearchable:not(.vs--disabled) .vs__search {
  cursor: pointer; }

.vs--single.vs--searching:not(.vs--open):not(.vs--loading) .vs__search {
  opacity: .2; }

/* Loading Spinner */
.vs__spinner {
  align-self: center;
  opacity: 0;
  font-size: 5px;
  text-indent: -9999em;
  overflow: hidden;
  border-top: 0.9em solid rgba(100, 100, 100, 0.1);
  border-right: 0.9em solid rgba(100, 100, 100, 0.1);
  border-bottom: 0.9em solid rgba(100, 100, 100, 0.1);
  border-left: 0.9em solid rgba(60, 60, 60, 0.45);
  transform: translateZ(0);
  animation: vSelectSpinner 1.1s infinite linear;
  transition: opacity .1s; }

.vs__spinner,
.vs__spinner:after {
  border-radius: 50%;
  width: 5em;
  height: 5em; }

/* Loading Spinner States */
.vs--loading .vs__spinner {
  opacity: 1; }
.mt-5[data-v-499928] {
    margin-top: 15px;
}