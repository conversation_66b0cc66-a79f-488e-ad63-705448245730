<template>
  <button class="btn botao-dropdown-item" @click="handleClick" id="diana_descricao_completa_resumida" :disabled="isDisabled" v-if="exibirBotao">
    DIANA Descrição proposta completa e resumida
  </button>
</template>
<script>
export default {
  data() {
    return {
      url: $base_url + "atribuir_grupo/xhr_sugerir_descricao_diana_completa_e_resumida",
      isDisabled: false,
      exibirBotao: true,
    }
  },
  props: {
    id_empresa: {
      required: true,
      type: String | Number
    }
  },
  methods: {
      async handleClick() {
        const selectedItems = this.buscarItensSelecionados();
        if (selectedItems.length === 0) {
          swal('Atenção!', 'Nenhum item foi selecionado!', 'warning');
          return;
        }

        if (
          ($('#descricao_mercado_local').val() != null && $('#descricao_mercado_local').val() != '')
          || ($('#descricao_proposta_completa').val() != null && $('#descricao_proposta_completa').val() != '')
        ) {
          return;
        }

        this.buscarSugestaoDescricaoCompletaResumidaDiana(selectedItems);
      },
      buscarItensSelecionados() {
        const id_empresa = this.id_empresa;
        return $(".item_selected:checked")
          .map(function () {
            return {
              'partnumber': $(this).val(),
              'description': $(this).data('descricao'),
              'establishment': $(this).data('estabelecimento'),
              'company_id': id_empresa,
            }
          })
          .get();
      },
      buscarSugestaoDescricaoCompletaResumidaDiana(selectedItems) {
        $('#loading-overlay-diana').show();

        const postData = {
          dataItem: selectedItems,
        };
        $.post(this.url, postData, function (response) {
          if (response && response.status === 200 && response.data) {
            if (response.data.descricao_completa && response.data.descricao_completa[0] && response.data.descricao_completa[0].detailed_description) {
              $('#descricao_proposta_completa').val(response.data.descricao_completa[0].detailed_description);
            }
            if (response.data.descricao_resumida && response.data.descricao_resumida[0] && response.data.descricao_resumida[0].summary_description) {
              $('#descricao_mercado_local').val(response.data.descricao_resumida[0].summary_description);
            }
          }
          swal('Sucesso!', 'Sugestão de descrição completa e resumida Diana aplicada.', 'success');
          $('#loading-overlay-diana').hide();
          return true;
        }).fail(function (error) {
          console.error('Erro ao buscar as sugestões de descrição completa e resumida Diana:', error);
          swal('Erro!', 'Erro ao buscar as sugestões de descrição completa e resumida Diana.', 'error');
          $('#loading-overlay-diana').hide();
          return true;
        });
      }
  },
  mounted() {
    this._descricaoInputCompleta$ = $('#descricao_proposta_completa');
    this._descricaoInputResumida$ = $('#descricao_mercado_local');
    this._handleUpdateDisabledCompleta = () => {
      const value = this._descricaoInputCompleta$ && this._descricaoInputCompleta$.length ? this._descricaoInputCompleta$.val() : '';
      this.isDisabled = value != null && String(value).trim() !== '';
    };
    this._handleUpdateDisabledResumida = () => {
      const value = this._descricaoInputResumida$ && this._descricaoInputResumida$.length ? this._descricaoInputResumida$.val() : '';
      this.isDisabled = value != null && String(value).trim() !== '';
    };

    this._modal$ = $('#modal_motivo');
    this._onModalShown = () => {
      if ($(".item_selected:checked").length > 1) {
        this.exibirBotao = false;
      }
      if (this._descricaoInputCompleta$ && this._handleUpdateDisabledCompleta) {
        this._descricaoInputCompleta$.off('input change', this._handleUpdateDisabledCompleta);
      }
      if (this._descricaoInputResumida$ && this._handleUpdateDisabledResumida) {
        this._descricaoInputResumida$.off('input change', this._handleUpdateDisabledResumida);
      }
      this._descricaoInputCompleta$ = $('#descricao_proposta_completa');
      this._descricaoInputResumida$ = $('#descricao_mercado_local');
      if (this._descricaoInputCompleta$ && this._descricaoInputCompleta$.length) {
        this._descricaoInputCompleta$.on('input change', this._handleUpdateDisabledCompleta);
        this._handleUpdateDisabledCompleta();
      }
      if (this._descricaoInputResumida$ && this._descricaoInputResumida$.length) {
        this._descricaoInputResumida$.on('input change', this._handleUpdateDisabledResumida);
        this._handleUpdateDisabledResumida();
      }
    };
    if (this._modal$ && this._modal$.length) {
      this._modal$.on('shown.bs.modal.btnDianaCompletaResumida', this._onModalShown);
    }
  },
  beforeDestroy() {
    if (this._modal$ && this._onModalShown) {
      this._modal$.off('shown.bs.modal.btnDianaCompletaResumida', this._onModalShown);
    }
  }
}
</script>